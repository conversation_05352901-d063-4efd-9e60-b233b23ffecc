import { z } from "zod";

// Bid schema
export const bidSchema = z.object({
  productId: z.string().uuid("Invalid product ID"),
  amount: z.number().min(0.01, "Bid amount must be greater than 0"),
});

// Product in bid schema
export const productInBidSchema = z.object({
  id: z.string().uuid(),
  itemName: z.string(),
  slug: z.string().nullable(),
  priceUSD: z.number(),
  currentBid: z.number().nullable(),
  bidCount: z.number().int(),
  auctionStartDate: z.string().datetime().nullable(),
  auctionEndDate: z.string().datetime().nullable(),
  status: z.string(),
  sellType: z.enum(["auction", "buy-now"]),
  images: z.array(z.object({
    id: z.string().uuid(),
    imageUrl: z.string(),
    altText: z.string().nullable(),
    sortOrder: z.number().int(),
    isMain: z.boolean(),
  })),
  seller: z.object({
    id: z.string().uuid(),
    name: z.string(),
    email: z.string(),
  }),
});

// Bidder schema
export const bidderSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  email: z.string(),
});

// Bid response schema
export const bidResponseSchema = z.object({
  id: z.string().uuid(),
  productId: z.string().uuid(),
  bidderId: z.string().uuid(),
  amount: z.number(),
  isWinning: z.boolean(),
  createdAt: z.string().datetime(),
  bidder: bidderSchema,
  product: productInBidSchema,
});

// Bid history schema
export const bidHistorySchema = z.object({
  productId: z.string().uuid(),
  bids: z.array(z.object({
    id: z.string().uuid(),
    amount: z.number(),
    isWinning: z.boolean(),
    createdAt: z.string().datetime(),
    bidder: z.object({
      id: z.string().uuid(),
      name: z.string(),
    }),
  })),
  totalBids: z.number().int(),
  highestBid: z.number().nullable(),
  currentWinner: bidderSchema.nullable(),
});

// User bid summary schema
export const userBidSummarySchema = z.object({
  productId: z.string().uuid(),
  product: z.object({
    id: z.string().uuid(),
    itemName: z.string(),
    slug: z.string().nullable(),
    currentBid: z.number().nullable(),
    auctionEndDate: z.string().datetime().nullable(),
    status: z.string(),
    images: z.array(z.object({
      id: z.string().uuid(),
      imageUrl: z.string(),
      isMain: z.boolean(),
    })),
  }),
  highestBid: z.number(),
  totalBids: z.number().int(),
  isWinning: z.boolean(),
  lastBidTime: z.string().datetime(),
  auctionStatus: z.enum(["active", "ended", "won", "lost"]),
});

// User bids list response schema
export const userBidsListResponseSchema = z.object({
  bids: z.array(userBidSummarySchema),
  pagination: z.object({
    page: z.number().int(),
    limit: z.number().int(),
    total: z.number().int(),
    totalPages: z.number().int(),
  }),
});

// Query schema for user bids
export const userBidsQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().int().min(1)).default("1"),
  limit: z.string().transform(Number).pipe(z.number().int().min(1).max(100)).default("10"),
  status: z.enum(["active", "ended", "won", "lost"]).optional(),
  sortBy: z.enum(["createdAt", "amount", "auctionEndDate"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// Export types
export type BidData = z.infer<typeof bidSchema>;
export type ProductInBid = z.infer<typeof productInBidSchema>;
export type Bidder = z.infer<typeof bidderSchema>;
export type BidResponse = z.infer<typeof bidResponseSchema>;
export type BidHistory = z.infer<typeof bidHistorySchema>;
export type UserBidSummary = z.infer<typeof userBidSummarySchema>;
export type UserBidsListResponse = z.infer<typeof userBidsListResponseSchema>;
export type UserBidsQuery = z.infer<typeof userBidsQuerySchema>;
