import { z } from "zod";

export const loginSchema = z.object({
  email: z.string().min(1, "Email or phone number is required"),
  password: z.string().min(1, "Password is required"),
});

export const registerSchema = z.object({
  firstName: z.string().min(1, "First name is required").max(50),
  lastName: z.string().min(1, "Last name is required").max(50),
  email: z.string().email("Invalid email format"),
  phoneNumber: z
    .string()
    .regex(
      /^\([1-9][0-9]{0,3}\)\s?[0-9]{6,13}$/,
      "Nomor handphone tidak valid. Gunakan format seperti: 6281234567890 atau 821234567890"
    ),
  password: z.string().min(8, "Password minimal 8 karakter"),
  confirmPassword: z.string().min(8, "Konfirmasi password minimal 8 karakter")
});

export const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, "Refresh token is required"),
});

export const googleAuthSchema = z.object({
  googleToken: z.string().min(1, "Google token is required"),
});