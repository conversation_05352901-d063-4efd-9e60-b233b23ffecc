import { z } from "zod";

export const addToCartSchema = z.object({
  productId: z.string().uuid("Invalid product ID"),
  quantity: z.number().int().min(1, "Quantity must be at least 1").max(100, "Quantity cannot exceed 100"),
});

export const updateCartItemSchema = z.object({
  quantity: z.number().int().min(1, "Quantity must be at least 1").max(100, "Quantity cannot exceed 100"),
});

export const productImageSchema = z.object({
  id: z.string().uuid(),
  imageUrl: z.string(),
  altText: z.string().nullable(),
  sortOrder: z.number().int(),
  isMain: z.boolean(),
});

export const productInCartSchema = z.object({
  id: z.string().uuid(),
  itemName: z.string(),
  slug: z.string().nullable(),
  priceUSD: z.number(),
  sellType: z.enum(["auction", "buy-now"]),
  status: z.string(),
  images: z.array(productImageSchema),
});

export const cartItemResponseSchema = z.object({
  id: z.string().uuid(),
  cartId: z.string().uuid(),
  productId: z.string().uuid(),
  quantity: z.number().int(),
  price: z.number(),
  product: productInCartSchema,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const cartResponseSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  items: z.array(cartItemResponseSchema),
  totalItems: z.number().int(),
  totalPrice: z.number(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Export types
export type AddToCartData = z.infer<typeof addToCartSchema>;
export type UpdateCartItemData = z.infer<typeof updateCartItemSchema>;
export type CartItemResponse = z.infer<typeof cartItemResponseSchema>;
export type CartResponse = z.infer<typeof cartResponseSchema>;
export type ProductInCart = z.infer<typeof productInCartSchema>;
export type ProductImage = z.infer<typeof productImageSchema>;
