import { Context, Next } from 'hono';
import { verify } from 'hono/jwt';
import { prisma } from '../db';

const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export const rateLimitMiddleware = (maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) => {
  return async (c: Context, next: Next) => {
    const clientIp = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown';
    const now = Date.now();

    const clientData = rateLimitStore.get(clientIp);

    if (!clientData || now > clientData.resetTime) {
      rateLimitStore.set(clientIp, { count: 1, resetTime: now + windowMs });
      await next();
      return;
    }

    if (clientData.count >= maxRequests) {
      return c.json(
        { success: false, message: 'Too many requests. Please try again later.' },
        429
      );
    }

    clientData.count++;
    await next();
  };
};

export const authMiddleware = async (c: Context, next: Next) => {
  try {
    const authHeader = c.req.header('Authorization');

    if (!authHeader) {
      return c.json(
        { success: false, message: 'Authorization header is missing' },
        401
      );
    }

    const bearerRegex = /^Bearer\s+([A-Za-z0-9\-_=]+\.[A-Za-z0-9\-_=]+\.?[A-Za-z0-9\-_.+/=]*)$/;
    const match = authHeader.match(bearerRegex);

    if (!match) {
      return c.json(
        { success: false, message: 'Invalid token format. Use: Bearer <token>' },
        401
      );
    }

    const token = match[1];
    const secret = process.env.JWT_SECRET;

    if (!secret) {
      console.error('JWT_SECRET is not configured');
      return c.json(
        { success: false, message: 'Server configuration error' },
        500
      );
    }

    const payload = await verify(token, secret) as any;

    if (!payload.id || !payload.email) {
      return c.json(
        { success: false, message: 'Invalid token payload' },
        401
      );
    }

    if( !payload.exp || payload.exp < Math.floor(Date.now() / 1000)) {
      return c.json(
        { success: false, message: 'Token has expired' },
        401
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: payload.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phoneNumber: true,
        isActive: true,
        isBlocked: true,
        lastLogin: true,
      }
    });

    if (!user) {
      return c.json(
        { success: false, message: 'User not found' },
        401
      );
    }

    if (!user.isActive || user.isBlocked) {
      return c.json(
        { success: false, message: 'Account is inactive or blocked' },
        401
      );
    }

    await prisma.user.update({
      where: { id: user.id },
      data: {
        lastLogin: new Date(),
        lastLoginIp: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
      }
    });

    c.set('user', {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phoneNumber: user.phoneNumber,
    });

    await next();
  } catch (error) {
    console.error('Auth middleware error:', error);

    if (error instanceof Error) {
      if (error.message.includes('expired')) {
        return c.json(
          { success: false, message: 'Token has expired' },
          401
        );
      }
      if (error.message.includes('invalid')) {
        return c.json(
          { success: false, message: 'Invalid token' },
          401
        );
      }
    }

    return c.json(
      { success: false, message: 'Authentication failed' },
      401
    );
  }
};

export const adminMiddleware = async (c: Context, next: Next) => {
  const user = c.get('user');

  if (!user) {
    return c.json(
      { success: false, message: 'Authentication required' },
      401
    );
  }

  const userRecord = await prisma.user.findUnique({
    where: { id: user.id },
    select: { role: true }
  });

  if (!userRecord || userRecord.role !== 'admin') {
    return c.json(
      { success: false, message: 'Admin access required' },
      403
    );
  }

  await next();
};