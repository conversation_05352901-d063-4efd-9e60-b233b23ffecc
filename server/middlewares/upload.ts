import { Context } from 'hono';
import { v2 as cloudinary } from 'cloudinary';
import { Readable } from 'stream';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

interface UploadedFile {
  name: string;
  type: string;
  size: number;
  arrayBuffer: ArrayBuffer;
}

// Helper function to upload buffer to Cloudinary
const uploadToCloudinary = (buffer: Buffer, filename: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const uploadStream = cloudinary.uploader.upload_stream(
      {
        resource_type: 'image',
        folder: 'king-collectibles/products',
        public_id: `${Date.now()}-${filename}`,
        transformation: [
          { width: 1200, height: 1200, crop: 'limit', quality: 'auto' },
          { format: 'webp' }
        ]
      },
      (error, result) => {
        if (error) {
          reject(error);
        } else {
          resolve(result);
        }
      }
    );

    const stream = Readable.from(buffer);
    stream.pipe(uploadStream);
  });
};

// Parse multipart form data for Hono
// type UploadedFile = {
//   name: string;
//   type: string;
//   size: number;
//   arrayBuffer: ArrayBuffer;
// };

export const parseMultipartData = async (c: Context): Promise<UploadedFile[]> => {
  try {
    const body = await c.req.parseBody();
    const files: UploadedFile[] = [];
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    console.log('Parsed multipart body:', body);

    for (const [key, value] of Object.entries(body)) {
      // Support for single file
      if (value instanceof File) {
        if (allowedTypes.includes(value.type) && value.size <= 5 * 1024 * 1024) {
          files.push({
            name: value.name,
            type: value.type,
            size: value.size,
            arrayBuffer: await value.arrayBuffer()
          });
        } else {
          throw new Error(`File "${value.name}" is invalid or too large.`);
        }
      }

      // Support for multiple files (e.g., "images[]" as key)
      if (Array.isArray(value)) {
        for (const file of value) {
          if (file instanceof File) {
            if (allowedTypes.includes(file.type) && file.size <= 5 * 1024 * 1024) {
              files.push({
                name: file.name,
                type: file.type,
                size: file.size,
                arrayBuffer: await file.arrayBuffer()
              });
            } else {
              throw new Error(`File "${file.name}" is invalid or too large.`);
            }
          }
        }
      }
    }

    return files;
  } catch (error) {
    console.error('Error parsing multipart data:', error);
    throw error;
  }
};


// Service function to upload images to Cloudinary
export const uploadImagesToCloudinary = async (files: UploadedFile[]): Promise<string[]> => {
  try {
    const uploadPromises = files.map(async (file) => {
      const buffer = Buffer.from(file.arrayBuffer);
      const result = await uploadToCloudinary(buffer, file.name);
      return result.secure_url;
    });

    const urls = await Promise.all(uploadPromises);
    return urls;
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    throw new Error('Failed to upload images to cloud storage');
  }
};

// Alternative: Local file storage (for development)
export const saveFilesLocally = async (files: UploadedFile[]): Promise<string[]> => {
  const fs = await import('fs/promises');
  const path = await import('path');

  const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'products');

  // Ensure upload directory exists
  try {
    await fs.access(uploadDir);
  } catch {
    await fs.mkdir(uploadDir, { recursive: true });
  }

  const urls: string[] = [];

  for (const file of files) {
    const filename = `${Date.now()}-${file.name}`;
    const filepath = path.join(uploadDir, filename);

    const buffer = Buffer.from(file.arrayBuffer);
    await fs.writeFile(filepath, buffer);
    urls.push(`/uploads/products/${filename}`);
  }

  return urls;
};
