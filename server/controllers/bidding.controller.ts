import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import biddingService from "../services/bidding.service";

class BiddingController {
  // Get user's bidding history
  async getUserBids(c: Context) {
    try {
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const rawQuery = c.req.query();
      const query = {
        page: parseInt(rawQuery.page || '1'),
        limit: parseInt(rawQuery.limit || '10'),
        status: rawQuery.status as any,
        sortBy: (rawQuery.sortBy || 'createdAt') as any,
        sortOrder: (rawQuery.sortOrder || 'desc') as any
      };

      const result = await biddingService.getUserBids(user.id, query);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get user bids controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  // Get single bid detail
  async getBidDetail(c: Context) {
    try {
      const bidId = c.req.param('bidId');
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await biddingService.getBidDetail(user.id, bidId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get bid detail controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  // Get bid history for a product
  async getBidHistory(c: Context) {
    try {
      const productId = c.req.param('productId');

      const result = await biddingService.getBidHistory(productId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get bid history controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

export default new BiddingController();
