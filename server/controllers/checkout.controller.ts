import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import checkoutService from "../services/checkout.service";

class CheckoutController {
  // Shipping Address Controllers
  async createShippingAddress(c: Context) {
    try {
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.createShippingAddress(user.id, body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create shipping address controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getShippingAddresses(c: Context) {
    try {
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.getShippingAddresses(user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get shipping addresses controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async updateShippingAddress(c: Context) {
    try {
      const addressId = c.req.param('addressId');
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.updateShippingAddress(user.id, addressId, body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Update shipping address controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async deleteShippingAddress(c: Context) {
    try {
      const addressId = c.req.param('addressId');
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.deleteShippingAddress(user.id, addressId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Delete shipping address controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  // Order Controllers
  async createOrderFromCart(c: Context) {
    try {
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.createOrderFromCart(user.id, body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create order from cart controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async createBuyNowOrder(c: Context) {
    try {
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.createBuyNowOrder(user.id, body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create buy now order controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getOrders(c: Context) {
    try {
      const rawQuery = c.req.query();
      const query = {
        page: parseInt(rawQuery.page || '1'),
        limit: parseInt(rawQuery.limit || '10'),
        status: rawQuery.status,
        paymentStatus: rawQuery.paymentStatus,
        sortBy: rawQuery.sortBy || 'createdAt',
        sortOrder: rawQuery.sortOrder || 'desc'
      };

      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.getOrders(user.id, query);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get orders controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getOrder(c: Context) {
    try {
      const orderId = c.req.param('orderId');
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.getOrder(user.id, orderId);

      if (!result.status) {
        return c.json(result, 404);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get order controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async updateOrderStatus(c: Context) {
    try {
      const orderId = c.req.param('orderId');
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      // For now, only allow admin users to update order status
      if (user.role !== 'admin') {
        return c.json(errorResponse("Forbidden - Admin access required"), 403);
      }

      const result = await checkoutService.updateOrderStatus(orderId, body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Update order status controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

export default new CheckoutController();
