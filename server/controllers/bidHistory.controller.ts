import { Context } from "hono";
import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";
// Auth middleware is handled in routes, not needed here

class BidHistoryController {
  /**
   * Get bid history for a specific product
   */
  async getBidHistory(c: Context) {
    try {
      const productId = c.req.param('productId');
      const { page = '1', limit = '20' } = c.req.query();

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      // Verify product exists
      const product = await prisma.product.findUnique({
        where: { id: productId },
        select: { id: true, sellType: true, status: true }
      });

      if (!product) {
        return c.json(errorResponse("Product not found"), 404);
      }

      if (product.sellType !== 'auction') {
        return c.json(errorResponse("Bid history is only available for auction products"), 400);
      }

      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;

      // Get bid history with bidder information
      const bids = await prisma.bid.findMany({
        where: { productId },
        include: {
          bidder: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true
            }
          }
        },
        orderBy: [
          { amount: 'desc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limitNum
      });

      // Get total count for pagination
      const totalBids = await prisma.bid.count({
        where: { productId }
      });

      // Format bid history data
      const bidHistory = bids.map(bid => ({
        id: bid.id,
        amount: Number(bid.amount),
        currency: 'USD', // All bids are stored in USD
        bidTime: bid.createdAt,
        bidder: {
          id: bid.bidder.id,
          name: `${bid.bidder.firstName || ''} ${bid.bidder.lastName || ''}`.trim() || 'Anonymous',
          email: bid.bidder.email,
          profileImage: bid.bidder.image,
          // Hide email for privacy, only show first letter and domain
          displayEmail: bid.bidder.email ? 
            `${bid.bidder.email.charAt(0)}***@${bid.bidder.email.split('@')[1]}` : 
            null
        },
        isWinning: Number(bid.amount) === Math.max(...bids.map(b => Number(b.amount))),
        bidNumber: bids.findIndex(b => b.id === bid.id) + 1
      }));

      // Get current highest bid
      const currentHighestBid = bids.length > 0 ? Math.max(...bids.map(b => Number(b.amount))) : 0;

      // Get bid statistics
      const bidStats = {
        totalBids: totalBids,
        uniqueBidders: await prisma.bid.groupBy({
          by: ['bidderId'],
          where: { productId },
          _count: true
        }).then(groups => groups.length),
        currentHighestBid,
        averageBid: bids.length > 0 ? 
          bids.reduce((sum, bid) => sum + Number(bid.amount), 0) / bids.length : 0,
        bidRange: {
          lowest: bids.length > 0 ? Math.min(...bids.map(b => Number(b.amount))) : 0,
          highest: currentHighestBid
        }
      };

      return c.json(successResponse("Bid history retrieved successfully", {
        bidHistory,
        stats: bidStats,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: totalBids,
          totalPages: Math.ceil(totalBids / limitNum),
          hasNext: pageNum * limitNum < totalBids,
          hasPrev: pageNum > 1
        }
      }));

    } catch (error) {
      console.error('Get bid history error:', error);
      return c.json(errorResponse("Failed to retrieve bid history"), 500);
    }
  }

  /**
   * Get user's bid history for a specific product
   */
  async getUserBidHistory(c: Context) {
    try {
      const user = c.get('user');
      const productId = c.req.param('productId');

      if (!user) {
        return c.json(errorResponse("Authentication required"), 401);
      }

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      // Get user's bids for this product
      const userBids = await prisma.bid.findMany({
        where: { 
          productId,
          bidderId: user.id
        },
        orderBy: { createdAt: 'desc' }
      });

      // Get current highest bid for comparison
      const allBids = await prisma.bid.findMany({
        where: { productId },
        orderBy: { amount: 'desc' },
        take: 1
      });

      const currentHighestBid = allBids.length > 0 ? Number(allBids[0].amount) : 0;

      const formattedBids = userBids.map((bid, index) => ({
        id: bid.id,
        amount: Number(bid.amount),
        currency: 'USD',
        bidTime: bid.createdAt,
        isWinning: Number(bid.amount) === currentHighestBid,
        isOutbid: Number(bid.amount) < currentHighestBid,
        bidNumber: userBids.length - index // Most recent bid gets highest number
      }));

      return c.json(successResponse("User bid history retrieved successfully", {
        userBids: formattedBids,
        totalUserBids: userBids.length,
        highestUserBid: userBids.length > 0 ? Math.max(...userBids.map(b => Number(b.amount))) : 0,
        isCurrentWinner: userBids.some(bid => Number(bid.amount) === currentHighestBid)
      }));

    } catch (error) {
      console.error('Get user bid history error:', error);
      return c.json(errorResponse("Failed to retrieve user bid history"), 500);
    }
  }

  /**
   * Get bid activity summary for a product
   */
  async getBidActivitySummary(c: Context) {
    try {
      const productId = c.req.param('productId');

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      // Get recent bid activity (last 24 hours)
      const recentBids = await prisma.bid.findMany({
        where: { 
          productId,
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        include: {
          bidder: {
            select: {
              firstName: true,
              lastName: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      });

      const activity = recentBids.map(bid => ({
        id: bid.id,
        amount: Number(bid.amount),
        bidderName: `${bid.bidder.firstName || ''} ${bid.bidder.lastName || ''}`.trim() || 'Anonymous',
        timeAgo: this.getTimeAgo(bid.createdAt),
        bidTime: bid.createdAt
      }));

      return c.json(successResponse("Bid activity summary retrieved successfully", {
        recentActivity: activity,
        last24Hours: {
          totalBids: recentBids.length,
          uniqueBidders: [...new Set(recentBids.map(b => b.bidderId))].length,
          highestBid: recentBids.length > 0 ? Math.max(...recentBids.map(b => Number(b.amount))) : 0
        }
      }));

    } catch (error) {
      console.error('Get bid activity summary error:', error);
      return c.json(errorResponse("Failed to retrieve bid activity summary"), 500);
    }
  }

  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds} seconds ago`;
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  }
}

export default new BidHistoryController();
