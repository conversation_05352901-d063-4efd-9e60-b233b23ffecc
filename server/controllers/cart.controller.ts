import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import cartService from "../services/cart.service";
import { AddToCartData, UpdateCartItemData } from "../schemas/cart.schema";

class CartController {
  async getCart(c: Context) {
    try {
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await cartService.getCart(user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get cart controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async addToCart(c: Context) {
    try {
      const body: AddToCartData = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const { productId, quantity } = body;

      const result = await cartService.addToCart(user.id, productId, quantity);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Add to cart controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async updateCartItem(c: Context) {
    try {
      const itemId = c.req.param('itemId');
      const body: UpdateCartItemData = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const { quantity } = body;

      const result = await cartService.updateCartItem(user.id, itemId, quantity);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Update cart item controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async removeFromCart(c: Context) {
    try {
      const itemId = c.req.param('itemId');
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await cartService.removeFromCart(user.id, itemId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Remove from cart controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async clearCart(c: Context) {
    try {
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await cartService.clearCart(user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Clear cart controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

export default new CartController();
