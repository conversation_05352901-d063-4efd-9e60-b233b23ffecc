import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import masterService from "../services/master.service";

class MasterController {
  async getCategories(c: Context) {
    try {
      const rawQuery = c.req.query();
      const query = {
        sellType: rawQuery.sellType as 'auction' | 'buy-now' | undefined,
      };
      const result = await masterService.getCategories(query);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get categories controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async createCategory(c: Context) {
    try {
      const body = await c.req.json();
      const result = await masterService.createCategory(body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create category controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async updateCategory(c: Context) {
    try {
      const id = c.req.param('id');
      const body = await c.req.json();
      const result = await masterService.updateCategory(id, body);

      if (!result.status) {
        return c.json(result, result.message.includes('not found') ? 404 : 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Update category controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async deleteCategory(c: Context) {
    try {
      const id = c.req.param('id');
      const result = await masterService.deleteCategory(id);

      if (!result.status) {
        return c.json(result, result.message.includes('not found') ? 404 : 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Delete category controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getItemTypes(c: Context) {
    try {
      const categoryId = c.req.query('categoryId');
      const result = await masterService.getItemTypes(categoryId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get item types controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async createItemType(c: Context) {
    try {
      const body = await c.req.json();
      const result = await masterService.createItemType(body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create item type controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async updateItemType(c: Context) {
    try {
      const id = c.req.param('id');
      const body = await c.req.json();
      const result = await masterService.updateItemType(id, body);

      if (!result.status) {
        return c.json(result, result.message.includes('not found') ? 404 : 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Update item type controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async deleteItemType(c: Context) {
    try {
      const id = c.req.param('id');
      const result = await masterService.deleteItemType(id);

      if (!result.status) {
        return c.json(result, result.message.includes('not found') ? 404 : 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Delete item type controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

export default new MasterController();
