import { Context } from 'hono';
import { errorResponse } from '../utils/response.util';
import paymentRedirectService from '../services/paymentRedirect.service';

class PaymentRedirectController {
  /**
   * Create payment and get redirect URL
   */
  async createPaymentRedirect(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const body = await c.req.json();
      const {
        orderId,
        paymentMethod,
        paymentChannel,
        customerInfo,
        successUrl,
        failureUrl
      } = body;

      // Validate required fields
      if (!orderId || !paymentMethod) {
        return c.json(errorResponse("Order ID and payment method are required"), 400);
      }

      if (!customerInfo || !customerInfo.name || !customerInfo.email) {
        return c.json(errorResponse("Customer name and email are required"), 400);
      }

      // Set default URLs if not provided
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
      const defaultSuccessUrl = successUrl || `${baseUrl}/account/buying/${orderId}?payment=success`;
      const defaultFailureUrl = failureUrl || `${baseUrl}/account/buying/${orderId}?payment=failed`;

      const result = await paymentRedirectService.createPaymentAndRedirect({
        orderId,
        paymentMethod,
        paymentChannel,
        customerInfo,
        successUrl: defaultSuccessUrl,
        failureUrl: defaultFailureUrl
      });

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "Payment redirect created successfully",
        data: result.data
      });

    } catch (error) {
      console.error('Create payment redirect error:', error);
      return c.json(errorResponse("Failed to create payment redirect"), 500);
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const orderId = c.req.param('orderId');

      if (!orderId) {
        return c.json(errorResponse("Order ID is required"), 400);
      }

      const result = await paymentRedirectService.getPaymentStatus(orderId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "Payment status retrieved successfully",
        data: result.data
      });

    } catch (error) {
      console.error('Get payment status error:', error);
      return c.json(errorResponse("Failed to get payment status"), 500);
    }
  }

  /**
   * Handle payment success callback
   */
  async handlePaymentSuccess(c: Context) {
    const orderId = c.req.param('orderId');

    try {
      const { paymentId, status } = c.req.query();

      if (!orderId) {
        return c.json(errorResponse("Order ID is required"), 400);
      }

      // Update payment status
      // This would typically be handled by webhook, but we can also handle success callbacks

      // Redirect to success page
      const redirectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/account/buying/${orderId}?payment=success&paymentId=${paymentId}`;

      return c.redirect(redirectUrl);

    } catch (error) {
      console.error('Handle payment success error:', error);
      const redirectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/account/buying/${orderId || 'unknown'}?payment=failed`;
      return c.redirect(redirectUrl);
    }
  }

  /**
   * Handle payment failure callback
   */
  async handlePaymentFailure(c: Context) {
    const orderId = c.req.param('orderId');

    try {
      const { error, message } = c.req.query();

      if (!orderId) {
        return c.json(errorResponse("Order ID is required"), 400);
      }

      // Log the failure
      console.log(`Payment failed for order ${orderId}:`, { error, message });

      // Redirect to failure page
      const redirectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/account/buying/${orderId}?payment=failed&error=${encodeURIComponent(error || 'Payment failed')}`;

      return c.redirect(redirectUrl);

    } catch (error) {
      console.error('Handle payment failure error:', error);
      const redirectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/account/buying/${orderId || 'unknown'}?payment=failed`;
      return c.redirect(redirectUrl);
    }
  }

  /**
   * Get available payment methods for a specific currency and amount
   */
  async getAvailablePaymentMethods(c: Context) {
    try {
      const { currency = 'USD', amount = 0 } = c.req.query();

      const paymentMethods = {
        USD: [
          {
            type: 'xendit_invoice',
            name: 'Xendit Invoice',
            description: 'Pay with credit card, bank transfer, or e-wallet',
            channels: ['CREDIT_CARD'],
            minAmount: 1,
            maxAmount: 1000000
          }
        ],
        IDR: [
          {
            type: 'xendit_invoice',
            name: 'Xendit Invoice',
            description: 'Multiple payment options',
            channels: ['CREDIT_CARD', 'BCA', 'BNI', 'BRI', 'MANDIRI'],
            minAmount: 10000,
            maxAmount: ********
          },
          {
            type: 'ewallet',
            name: 'E-Wallet',
            description: 'Pay with digital wallet',
            channels: ['OVO', 'DANA', 'LINKAJA', 'SHOPEEPAY'],
            minAmount: 1000,
            maxAmount: ********
          },
          {
            type: 'virtual_account',
            name: 'Virtual Account',
            description: 'Bank transfer via virtual account',
            channels: ['BCA', 'BNI', 'BRI', 'MANDIRI', 'PERMATA'],
            minAmount: 10000,
            maxAmount: ********
          },
          {
            type: 'retail_outlet',
            name: 'Retail Outlet',
            description: 'Pay at convenience stores',
            channels: ['ALFAMART', 'INDOMARET'],
            minAmount: 10000,
            maxAmount: 2500000
          }
        ]
      };

      const availableMethods = paymentMethods[currency as keyof typeof paymentMethods] || paymentMethods.USD;
      
      // Filter by amount if provided
      const filteredMethods = Number(amount) > 0 
        ? availableMethods.filter(method => 
            Number(amount) >= method.minAmount && Number(amount) <= method.maxAmount
          )
        : availableMethods;

      return c.json({
        status: true,
        message: "Available payment methods retrieved successfully",
        data: {
          currency,
          amount: Number(amount),
          methods: filteredMethods
        }
      });

    } catch (error) {
      console.error('Get available payment methods error:', error);
      return c.json(errorResponse("Failed to get available payment methods"), 500);
    }
  }
}

const paymentRedirectController = new PaymentRedirectController();
export default paymentRedirectController;
