import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import orderService from "../services/order.service";

class OrderController {
  // Get user's orders
  async getUserOrders(c: Context) {
    try {
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const rawQuery = c.req.query();
      const query = {
        page: parseInt(rawQuery.page || '1'),
        limit: parseInt(rawQuery.limit || '10'),
        status: rawQuery.status as any,
        paymentStatus: rawQuery.paymentStatus as any,
        sortBy: (rawQuery.sortBy || 'createdAt') as any,
        sortOrder: (rawQuery.sortOrder || 'desc') as any
      };

      const result = await orderService.getUserOrders(user.id, query);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get user orders controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  // Get single order by ID
  async getOrderById(c: Context) {
    try {
      const orderId = c.req.param('orderId');
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await orderService.getOrderById(user.id, orderId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get order by ID controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  // Update order status (admin only)
  async updateOrderStatus(c: Context) {
    try {
      const orderId = c.req.param('orderId');
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      // For now, only allow admin users to update order status
      if (user.role !== 'admin') {
        return c.json(errorResponse("Forbidden - Admin access required"), 403);
      }

      const result = await orderService.updateOrderStatus(orderId, body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Update order status controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

export default new OrderController();
