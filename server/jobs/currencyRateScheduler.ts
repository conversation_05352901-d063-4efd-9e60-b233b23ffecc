import cron from 'node-cron';
import dailyCurrencyRateService from '../services/dailyCurrencyRate.service';

// Add delay to ensure database is ready
const STARTUP_DELAY = 5000; // 5 seconds

class CurrencyRateScheduler {
  private isRunning = false;

  /**
   * Start the currency rate scheduler
   */
  start() {
    console.log('Starting currency rate scheduler...');

    // Run every day at 6:00 AM Jakarta time (UTC+7)
    // This translates to 23:00 UTC (11:00 PM UTC)
    cron.schedule('0 23 * * *', async () => {
      if (this.isRunning) {
        console.log('Currency rate update already running, skipping...');
        return;
      }

      this.isRunning = true;
      console.log('Running scheduled currency rate update...');

      try {
        const result = await dailyCurrencyRateService.updateDailyRates();
        
        if (result.status) {
          console.log('✅ Scheduled currency rate update completed successfully');
          console.log('Rate data:', result.data);
        } else {
          console.error('❌ Scheduled currency rate update failed:', result.message);
        }
      } catch (error) {
        console.error('❌ Error during scheduled currency rate update:', error);
      } finally {
        this.isRunning = false;
      }
    }, {
      timezone: "Asia/Jakarta"
    });

    // Also run every 6 hours as backup
    cron.schedule('0 */6 * * *', async () => {
      if (this.isRunning) {
        console.log('Currency rate update already running, skipping backup update...');
        return;
      }

      this.isRunning = true;
      console.log('Running backup currency rate update...');

      try {
        const result = await dailyCurrencyRateService.updateDailyRates();
        
        if (result.status) {
          console.log('✅ Backup currency rate update completed successfully');
        } else {
          console.log('⚠️ Backup currency rate update failed, but this is expected if rates are already current');
        }
      } catch (error) {
        console.error('❌ Error during backup currency rate update:', error);
      } finally {
        this.isRunning = false;
      }
    });

    // Run initial update with delay to ensure database is ready
    setTimeout(() => {
      this.runInitialUpdate();
    }, STARTUP_DELAY);

    console.log('✅ Currency rate scheduler started successfully');
    console.log('📅 Daily updates scheduled for 6:00 AM Jakarta time');
    console.log('🔄 Backup updates every 6 hours');
  }

  /**
   * Run initial update on startup
   */
  private async runInitialUpdate() {
    if (this.isRunning) {
      console.log('Currency rate update already running, skipping initial update...');
      return;
    }

    this.isRunning = true;
    console.log('Running initial currency rate update...');

    try {
      const result = await dailyCurrencyRateService.updateDailyRates();

      if (result.status) {
        console.log('✅ Initial currency rate update completed successfully');
        console.log('Rate data:', result.data);
      } else {
        console.log('⚠️ Initial currency rate update completed with message:', result.message);
      }
    } catch (error) {
      console.error('❌ Error during initial currency rate update:', error);
      console.log('💡 This is normal on first startup - database may still be initializing');
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Stop the scheduler
   */
  stop() {
    console.log('Stopping currency rate scheduler...');
    cron.getTasks().forEach(task => task.stop());
    console.log('✅ Currency rate scheduler stopped');
  }

  /**
   * Get scheduler status
   */
  getStatus() {
    const tasks = cron.getTasks();
    return {
      isActive: tasks.size > 0,
      taskCount: tasks.size,
      isRunning: this.isRunning,
      nextRun: this.getNextRunTime()
    };
  }

  /**
   * Get next scheduled run time
   */
  private getNextRunTime() {
    // Calculate next 6:00 AM Jakarta time
    const now = new Date();
    const jakartaTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Jakarta"}));
    
    const nextRun = new Date(jakartaTime);
    nextRun.setHours(6, 0, 0, 0);
    
    // If it's already past 6 AM today, schedule for tomorrow
    if (jakartaTime.getHours() >= 6) {
      nextRun.setDate(nextRun.getDate() + 1);
    }
    
    return nextRun.toISOString();
  }

  /**
   * Force run currency update (for testing/manual trigger)
   */
  async forceUpdate() {
    if (this.isRunning) {
      throw new Error('Currency rate update is already running');
    }

    this.isRunning = true;
    console.log('Force running currency rate update...');

    try {
      const result = await dailyCurrencyRateService.updateDailyRates();
      return result;
    } finally {
      this.isRunning = false;
    }
  }
}

// Export singleton instance
const currencyRateScheduler = new CurrencyRateScheduler();
export default currencyRateScheduler;

// Auto-start if this file is run directly
if (require.main === module) {
  currencyRateScheduler.start();
  
  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\nReceived SIGINT, shutting down gracefully...');
    currencyRateScheduler.stop();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\nReceived SIGTERM, shutting down gracefully...');
    currencyRateScheduler.stop();
    process.exit(0);
  });
}
