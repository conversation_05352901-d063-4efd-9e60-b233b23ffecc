import { Hono } from 'hono';
import productPricingController from '../controllers/productPricing.controller';

const productPricingRoute = new Hono();

// Setup and initialization endpoints
productPricingRoute.post('/initialize', productPricingController.initializeRates);
productPricingRoute.get('/status', productPricingController.getRateStatus);

// Exchange rate endpoints
productPricingRoute.get('/rate', productPricingController.getCurrentRate);
productPricingRoute.post('/rate/refresh', productPricingController.refreshRate);

// Price conversion endpoints
productPricingRoute.post('/convert', productPricingController.convertPrice);
productPricingRoute.get('/test-conversion', productPricingController.testConversion);

// Product endpoints with pricing
productPricingRoute.get('/products', productPricingController.getProductsWithPrices);

export default productPricingRoute;
