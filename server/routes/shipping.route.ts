import { Hono } from 'hono';
import shippingController from '../controllers/shipping.controller';
import { authMiddleware } from '../middlewares/auth';

const shippingRoute = new Hono();

// Calculate shipping rates (public endpoint)
shippingRoute.post('/calculate', shippingController.calculateRates);

// Get shipping options for checkout (requires auth)
shippingRoute.post('/options', authMiddleware, shippingController.getShippingOptions);

// Track shipment (public endpoint)
shippingRoute.get('/track/:trackingNumber', shippingController.trackShipment);

// Get shipping zones (public endpoint)
shippingRoute.get('/zones', shippingController.getShippingZones);

// Estimate shipping cost for product (public endpoint)
shippingRoute.get('/estimate', shippingController.estimateShippingCost);

export default shippingRoute;
