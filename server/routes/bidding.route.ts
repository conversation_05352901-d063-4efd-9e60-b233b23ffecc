import { OpenAPIHono, createRoute, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import {
  userBidsQuerySchema,
  userBidsListResponseSchema,
  bidResponseSchema,
  bidHistorySchema
} from "../schemas/bidding.schema";
import biddingController from "../controllers/bidding.controller";
import { authMiddleware } from "../middlewares/auth";

const biddingRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

// Get User Bids Route
const getUserBidsRoute = createRoute({
  method: "get",
  path: "/",
  request: {
    query: userBidsQuerySchema,
  },
  responses: {
    200: {
      description: "Products retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            bids: z.any().array(),
            pagination: z.object({
              total: z.number(),
              page: z.number(),
              limit: z.number(),
              totalPages: z.number(),
            }),
          })
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    500: {
      description: "Internal server error",
    },
  },
});

// Get Bid Detail Route
const getBidDetailRoute = createRoute({
  method: "get",
  path: "/{bidId}",
  request: {
    params: z.object({
      bidId: z.string().uuid("Invalid bid ID"),
    }),
  },
  responses: {
    200: {
      description: "Bid detail retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: bidResponseSchema,
          }),
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    401: {
      description: "Unauthorized",
    },
    404: {
      description: "Bid not found",
    },
  },
});

// Get Bid History for Product Route (Public)
const getBidHistoryRoute = createRoute({
  method: "get",
  path: "/history/{productId}",
  request: {
    params: z.object({
      productId: z.string().uuid("Invalid product ID"),
    }),
  },
  responses: {
    200: {
      description: "Bid history retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: bidHistorySchema,
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    404: {
      description: "Product not found",
    },
  },
});

// Apply auth middleware to protected routes
biddingRoutes.use('/', authMiddleware);
biddingRoutes.use('/:bidId', authMiddleware);

// Register routes
biddingRoutes.openapi(getUserBidsRoute, biddingController.getUserBids);
biddingRoutes.openapi(getBidDetailRoute, biddingController.getBidDetail);

// Public route for bid history
biddingRoutes.openapi(getBidHistoryRoute, biddingController.getBidHistory);

export { biddingRoutes };
