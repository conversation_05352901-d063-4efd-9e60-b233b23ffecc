import { Hono } from 'hono';
import autoBidController from '../controllers/autoBid.controller';
import { authMiddleware } from '../middlewares/auth';

const autoBidRoute = new Hono();

// All auto-bid routes require authentication
autoBidRoute.use('*', authMiddleware);

// Enable auto-bid for a product
autoBidRoute.post('/enable', autoBidController.enableAutoBid);

// Disable auto-bid for a product
autoBidRoute.delete('/:productId', autoBidController.disableAutoBid);

// Get user's auto-bid settings for a specific product
autoBidRoute.get('/:productId', autoBidController.getUserAutoBid);

// Update auto-bid settings for a product
autoBidRoute.put('/:productId', autoBidController.updateAutoBid);

// Get all auto-bids for the authenticated user
autoBidRoute.get('/', autoBidController.getUserAutoBids);

// Get auto-bid statistics for the user
autoBidRoute.get('/stats/summary', autoBidController.getAutoBidStats);

export default autoBidRoute;
