import { Hono } from 'hono';
import dailyCurrencyRateController from '../controllers/dailyCurrencyRate.controller';
import { authMiddleware } from '../middlewares/auth';

const dailyCurrencyRateRoute = new Hono();

// Public endpoints
dailyCurrencyRateRoute.get('/current', dailyCurrencyRateController.getCurrentRates);
dailyCurrencyRateRoute.post('/convert', dailyCurrencyRateController.convertCurrency);
dailyCurrencyRateRoute.get('/history', dailyCurrencyRateController.getRateHistory);
dailyCurrencyRateRoute.get('/statistics', dailyCurrencyRateController.getRateStatistics);

// Admin endpoints (require authentication)
dailyCurrencyRateRoute.post('/update', authMiddleware, dailyCurrencyRateController.manualRateUpdate);

// Development/Testing endpoints
dailyCurrencyRateRoute.post('/test-update', dailyCurrencyRateController.testRateUpdate);
dailyCurrencyRateRoute.get('/test-connectivity', dailyCurrencyRateController.testApiConnectivity);
dailyCurrencyRateRoute.get('/live-rate', dailyCurrencyRateController.getLiveRate);

// Manual rate management
dailyCurrencyRateRoute.post('/create-manual', dailyCurrencyRateController.createManualRate);
dailyCurrencyRateRoute.post('/quick-setup', dailyCurrencyRateController.quickSetupRate);

// Cron job endpoint (should be protected by API key or internal access only)
dailyCurrencyRateRoute.post('/cron/update', dailyCurrencyRateController.updateDailyRates);

export default dailyCurrencyRateRoute;
