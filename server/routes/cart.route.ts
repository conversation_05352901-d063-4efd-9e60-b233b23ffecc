import { OpenAPIHono, createRoute, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import {
  addToCartSchema,
  updateCartItemSchema,
  cartResponseSchema,
  cartItemResponseSchema
} from "../schemas/cart.schema";
import cartController from "../controllers/cart.controller";
import { authMiddleware } from "../middlewares/auth";

const cartRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

// Get Cart Route
const getCartRoute = createRoute({
  method: "get",
  path: "/",
  responses: {
    200: {
      description: "Cart retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            totalItems: z.number(),
            totalPrice: z.number(),
            items: z.array(
              z.object({
                price: z.number(),
                product: z.object({
                  priceUSD: z.number(),
                  currentBid: z.number().nullable(),
                  images: z.array(
                    z.object({
                      createdAt: z.date(),
                      id: z.string(),
                      productId: z.string(),
                      sortOrder: z.number(),
                      imageUrl: z.string(),
                      altText: z.string().nullable(),
                      isMain: z.boolean(),
                    })
                  ),
                  status: z.string(),
                  createdAt: z.date(),
                  auctionEndDate: z.date().nullable(),
                  id: z.string(),
                  // ... add other product fields as needed ...
                  sellerId: z.string(),
                }),
                // ... add other cart item fields as needed ...
                cartId: z.string(),
              })
            ),
            createdAt: z.date(),
            id: z.string(),
            userId: z.string(),
            updatedAt: z.date(),
          }),
        }
      }
    },
    401: {
      description: "Unauthorized",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    500: {
      description: "Internal server error",
    },
  },
});

// Add to Cart Route
const addToCartRoute = createRoute({
  method: "post",
  path: "/items",
  request: {
    body: {
      content: {
        "application/json": {
          schema: addToCartSchema.openapi("AddToCartSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    201: {
      description: "Item added to cart successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: cartItemResponseSchema,
          }),
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Update Cart Item Route
const updateCartItemRoute = createRoute({
  method: "put",
  path: "/items/{itemId}",
  request: {
    params: z.object({
      itemId: z.string().uuid("Invalid item ID"),
    }),
    body: {
      content: {
        "application/json": {
          schema: updateCartItemSchema.openapi("UpdateCartItemSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Cart item updated successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: cartItemResponseSchema,
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Remove from Cart Route
const removeFromCartRoute = createRoute({
  method: "delete",
  path: "/items/{itemId}",
  request: {
    params: z.object({
      itemId: z.string().uuid("Invalid item ID"),
    }),
  },
  responses: {
    200: {
      description: "Item removed from cart successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Clear Cart Route
const clearCartRoute = createRoute({
  method: "delete",
  path: "/",
  responses: {
    200: {
      description: "Cart cleared successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Apply auth middleware to all routes
cartRoutes.use('/*', authMiddleware);

// Register routes
cartRoutes.openapi(getCartRoute, cartController.getCart);
cartRoutes.openapi(addToCartRoute, cartController.addToCart);
cartRoutes.openapi(updateCartItemRoute, cartController.updateCartItem);
cartRoutes.openapi(removeFromCartRoute, cartController.removeFromCart);
cartRoutes.openapi(clearCartRoute, cartController.clearCart);

export { cartRoutes };
