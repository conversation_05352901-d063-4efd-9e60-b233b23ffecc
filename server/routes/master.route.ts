import { OpenAPIHono, createRoute, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import { categorySchema, itemTypeSchema } from "../schemas/product.schema";
import masterController from "../controllers/master.controller";
import { authMiddleware } from "../middlewares/auth";

const masterRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

// Get Categories Route
const getCategoriesRoute = createRoute({
  method: "get",
  path: "/categories",
  responses: {
    200: {
      description: "Categories retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    500: {
      description: "Internal server error",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
  },
});

// Create Category Route
const createCategoryRoute = createRoute({
  method: "post",
  path: "/categories",
  request: {
    body: {
      content: {
        "application/json": {
          schema: categorySchema.openapi("CategorySchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    201: {
      description: "Category created successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.object({
              id: z.string(),
              name: z.string(),
              description: z.string().nullable(),
              isActive: z.boolean(),
              createdAt: z.string(),
              updatedAt: z.string()
            }),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Update Category Route
const updateCategoryRoute = createRoute({
  method: "put",
  path: "/categories/{id}",
  request: {
    params: z.object({
      id: z.string().uuid(),
    }),
    body: {
      content: {
        "application/json": {
          schema: categorySchema.partial().openapi("UpdateCategorySchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Category updated successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
    404: {
      description: "Category not found",
    },
  },
});

// Delete Category Route
const deleteCategoryRoute = createRoute({
  method: "delete",
  path: "/categories/{id}",
  request: {
    params: z.object({
      id: z.string().uuid(),
    }),
  },
  responses: {
    200: {
      description: "Category deleted successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
    404: {
      description: "Category not found",
    },
  },
});

// Get Item Types Route
const getItemTypesRoute = createRoute({
  method: "get",
  path: "/item-types",
  request: {
    query: z.object({
      categoryId: z.string().uuid().optional(),
    }),
  },
  responses: {
    200: {
      description: "Item types retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    500: {
      description: "Internal server error",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
  },
});

// Create Item Type Route
const createItemTypeRoute = createRoute({
  method: "post",
  path: "/item-types",
  request: {
    body: {
      content: {
        "application/json": {
          schema: itemTypeSchema.openapi("ItemTypeSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    201: {
      description: "Item type created successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Update Item Type Route
const updateItemTypeRoute = createRoute({
  method: "put",
  path: "/item-types/{id}",
  request: {
    params: z.object({
      id: z.string().uuid(),
    }),
    body: {
      content: {
        "application/json": {
          schema: itemTypeSchema.partial().openapi("UpdateItemTypeSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Item type updated successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
    404: {
      description: "Item type not found",
    },
  },
});

// Delete Item Type Route
const deleteItemTypeRoute = createRoute({
  method: "delete",
  path: "/item-types/{id}",
  request: {
    params: z.object({
      id: z.string().uuid(),
    }),
  },
  responses: {
    200: {
      description: "Item type deleted successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
    404: {
      description: "Item type not found",
    },
  },
});

// Public routes
masterRoutes.openapi(getCategoriesRoute, masterController.getCategories);
masterRoutes.openapi(getItemTypesRoute, masterController.getItemTypes);

// Protected routes (require authentication)
masterRoutes.use('/categories', authMiddleware);
masterRoutes.openapi(createCategoryRoute, masterController.createCategory);

masterRoutes.use('/categories/:id', authMiddleware);
masterRoutes.openapi(updateCategoryRoute, masterController.updateCategory);
masterRoutes.openapi(deleteCategoryRoute, masterController.deleteCategory);

masterRoutes.use('/item-types', authMiddleware);
masterRoutes.openapi(createItemTypeRoute, masterController.createItemType);

masterRoutes.use('/item-types/:id', authMiddleware);
masterRoutes.openapi(updateItemTypeRoute, masterController.updateItemType);
masterRoutes.openapi(deleteItemTypeRoute, masterController.deleteItemType);

export { masterRoutes };
