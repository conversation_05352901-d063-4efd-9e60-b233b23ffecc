import { Hono } from "hono";
import bidHistoryController from "../controllers/bidHistory.controller";
import { authMiddleware } from "../middlewares/auth";

const bidHistoryRoutes = new Hono();

// Public routes (no auth required)
bidHistoryRoutes.get('/product/:productId', bidHistoryController.getBidHistory);
bidHistoryRoutes.get('/product/:productId/activity', bidHistoryController.getBidActivitySummary);

// Protected routes (auth required)
bidHistoryRoutes.get('/product/:productId/user', authMiddleware, bidHistoryController.getUserBidHistory);

export default bidHistoryRoutes;
