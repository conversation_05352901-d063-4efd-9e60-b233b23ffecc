import { Hono } from 'hono';
import orderTrackingController from '../controllers/orderTracking.controller';
import { authMiddleware } from '../middlewares/auth';

const orderTrackingRoute = new Hono();

// Public endpoints (with optional auth for order owner verification)
orderTrackingRoute.get('/tracking/:orderId', authMiddleware, orderTrackingController.getOrderTracking);

// User endpoints (require authentication)
orderTrackingRoute.get('/user', authMiddleware, orderTrackingController.getUserOrders);
orderTrackingRoute.post('/create', authMiddleware, orderTrackingController.createOrderWithTracking);
orderTrackingRoute.post('/:orderId/cancel', authMiddleware, orderTrackingController.cancelOrder);

// Admin/Seller endpoints (require authentication and proper role)
orderTrackingRoute.put('/:orderId/status', authMiddleware, orderTrackingController.updateOrderStatus);
orderTrackingRoute.get('/admin/statistics', authMiddleware, orderTrackingController.getOrderStatistics);
orderTrackingRoute.get('/admin/search', authMiddleware, orderTrackingController.searchOrders);

export default orderTrackingRoute;
