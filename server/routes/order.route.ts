import { OpenAPIHono, createRoute, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import { 
  ordersQuerySchema,
  orderResponseSchema,
  ordersListResponseSchema,
  updateOrderStatusSchema
} from "../schemas/order.schema";
import orderController from "../controllers/order.controller";
import { authMiddleware } from "../middlewares/auth";

const orderRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

// Get User Orders Route
const getUserOrdersRoute = createRoute({
  method: "get",
  path: "/",
  request: {
    query: ordersQuerySchema,
  },
  responses: {
    200: {
      description: "Orders retrieved successfully",
      content: {
        "application/json": {
          schema: z.any(),
        }
      }
    },
    401: {
      description: "Unauthorized",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    500: {
      description: "Internal server error",
    },
  },
});

// Get Order by ID Route
const getOrderByIdRoute = createRoute({
  method: "get",
  path: "/{orderId}",
  request: {
    params: z.object({
      orderId: z.string().uuid("Invalid order ID"),
    }),
  },
  responses: {
    200: {
      description: "Order retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: orderResponseSchema,
          }),
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    401: {
      description: "Unauthorized",
    },
    404: {
      description: "Order not found",
    },
  },
});

// Update Order Status Route (Admin only)
const updateOrderStatusRoute = createRoute({
  method: "patch",
  path: "/{orderId}/status",
  request: {
    params: z.object({
      orderId: z.string().uuid("Invalid order ID"),
    }),
    body: {
      content: {
        "application/json": {
          schema: updateOrderStatusSchema.openapi("UpdateOrderStatusSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Order status updated successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: orderResponseSchema,
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
    403: {
      description: "Forbidden - Admin access required",
    },
    404: {
      description: "Order not found",
    },
  },
});

// Apply auth middleware to all routes
orderRoutes.use('/*', authMiddleware);

// Register routes
orderRoutes.openapi(getUserOrdersRoute, orderController.getUserOrders);
orderRoutes.openapi(getOrderByIdRoute, orderController.getOrderById);
orderRoutes.openapi(updateOrderStatusRoute, orderController.updateOrderStatus);

export { orderRoutes };
