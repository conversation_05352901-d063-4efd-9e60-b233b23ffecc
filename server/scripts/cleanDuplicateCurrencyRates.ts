import { PrismaClient } from "../../generated/client";

const prisma = new PrismaClient();

/**
 * <PERSON>ript to clean duplicate currency rates
 * Keeps the latest record for each unique combination of fromCurrency, toCurrency, and date
 */
async function cleanDuplicateCurrencyRates() {
  try {
    console.log('🧹 Starting cleanup of duplicate currency rates...');

    // Find all currency rates grouped by unique constraint fields
    const duplicateGroups = await prisma.$queryRaw`
      SELECT 
        "fromCurrency", 
        "toCurrency", 
        "date",
        COUNT(*) as count,
        array_agg("id" ORDER BY "updatedAt" DESC) as ids
      FROM "CurrencyRate" 
      GROUP BY "fromCurrency", "toCurrency", "date"
      HAVING COUNT(*) > 1
    ` as Array<{
      fromCurrency: string;
      toCurrency: string;
      date: Date;
      count: bigint;
      ids: string[];
    }>;

    console.log(`Found ${duplicateGroups.length} groups with duplicates`);

    let totalDeleted = 0;

    for (const group of duplicateGroups) {
      const { fromCurrency, toCurrency, date, ids } = group;
      
      // Keep the first ID (latest by updatedAt), delete the rest
      const [keepId, ...deleteIds] = ids;
      
      console.log(`Processing ${fromCurrency}/${toCurrency} on ${date}:`);
      console.log(`  Keeping: ${keepId}`);
      console.log(`  Deleting: ${deleteIds.join(', ')}`);

      // Delete duplicate records
      const deleteResult = await prisma.currencyRate.deleteMany({
        where: {
          id: {
            in: deleteIds
          }
        }
      });

      totalDeleted += deleteResult.count;
      console.log(`  Deleted ${deleteResult.count} duplicate records`);
    }

    console.log(`✅ Cleanup completed! Deleted ${totalDeleted} duplicate records`);

    // Verify no duplicates remain
    const remainingDuplicates = await prisma.$queryRaw`
      SELECT 
        "fromCurrency", 
        "toCurrency", 
        "date",
        COUNT(*) as count
      FROM "CurrencyRate" 
      GROUP BY "fromCurrency", "toCurrency", "date"
      HAVING COUNT(*) > 1
    ` as Array<{ count: bigint }>;

    if (remainingDuplicates.length === 0) {
      console.log('✅ No duplicate records remaining');
    } else {
      console.log(`⚠️ Warning: ${remainingDuplicates.length} duplicate groups still exist`);
    }

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Alternative cleanup method using application logic
 */
async function cleanDuplicatesWithAppLogic() {
  try {
    console.log('🧹 Starting cleanup with application logic...');

    // Get all currency rates
    const allRates = await prisma.currencyRate.findMany({
      orderBy: [
        { fromCurrency: 'asc' },
        { toCurrency: 'asc' },
        { date: 'asc' },
        { updatedAt: 'desc' }
      ]
    });

    console.log(`Found ${allRates.length} total currency rates`);

    const seen = new Set<string>();
    const toDelete: string[] = [];

    for (const rate of allRates) {
      const key = `${rate.fromCurrency}-${rate.toCurrency}-${rate.date.toISOString().split('T')[0]}`;
      
      if (seen.has(key)) {
        // This is a duplicate, mark for deletion
        toDelete.push(rate.id);
        console.log(`Marking duplicate for deletion: ${rate.id} (${key})`);
      } else {
        // First occurrence, keep it
        seen.add(key);
      }
    }

    console.log(`Found ${toDelete.length} duplicates to delete`);

    if (toDelete.length > 0) {
      // Delete in batches to avoid timeout
      const batchSize = 100;
      let deleted = 0;

      for (let i = 0; i < toDelete.length; i += batchSize) {
        const batch = toDelete.slice(i, i + batchSize);
        
        const result = await prisma.currencyRate.deleteMany({
          where: {
            id: {
              in: batch
            }
          }
        });

        deleted += result.count;
        console.log(`Deleted batch ${Math.floor(i / batchSize) + 1}: ${result.count} records`);
      }

      console.log(`✅ Cleanup completed! Deleted ${deleted} duplicate records`);
    } else {
      console.log('✅ No duplicates found');
    }

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the cleanup
if (require.main === module) {
  const method = process.argv[2] || 'sql';
  
  if (method === 'sql') {
    cleanDuplicateCurrencyRates();
  } else if (method === 'app') {
    cleanDuplicatesWithAppLogic();
  } else {
    console.log('Usage: npm run clean-currency-duplicates [sql|app]');
    console.log('  sql: Use raw SQL queries (faster)');
    console.log('  app: Use application logic (safer)');
  }
}

export { cleanDuplicateCurrencyRates, cleanDuplicatesWithAppLogic };
