import { errorResponse, successResponse } from '../utils/response.util';
import { prisma } from '../db';

interface ShippingCalculationData {
  fromCountry: string;
  fromCity: string;
  fromPostalCode: string;
  toCountry: string;
  toCity: string;
  toPostalCode: string;
  weight: number; // in kg
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  shippingMethod?: 'standard' | 'express' | 'overnight';
}

interface ShippingRate {
  carrier: string;
  service: string;
  cost: number;
  currency: string;
  estimatedDays: number;
  trackingAvailable: boolean;
}

class ShippingService {
  private readonly SHIPPO_API_KEY = process.env.SHIPPO_API_KEY;
  private readonly SHIPPO_BASE_URL = 'https://api.goshippo.com';
  private readonly DHL_API_KEY = process.env.DHL_API_KEY;
  private readonly DHL_API_SECRET = process.env.DHL_API_SECRET;
  private readonly DHL_BASE_URL = 'https://api-eu.dhl.com';
  private readonly FEDEX_API_KEY = process.env.FEDEX_API_KEY;
  private readonly FEDEX_SECRET_KEY = process.env.FEDEX_SECRET_KEY;
  private readonly FEDEX_BASE_URL = 'https://apis.fedex.com';

  /**
   * Calculate international shipping rates using multiple APIs
   */
  async calculateShippingRates(data: ShippingCalculationData): Promise<any> {
    try {
      // Try multiple shipping APIs in order of preference
      let rates: ShippingRate[] = [];

      // Try Shippo first (aggregates multiple carriers)
      if (this.SHIPPO_API_KEY) {
        const shippoRates = await this.getShippoRates(data);
        if (shippoRates && shippoRates.length > 0) {
          rates = rates.concat(shippoRates);
        }
      }

      // Try DHL API
      if (this.DHL_API_KEY && this.DHL_API_SECRET) {
        const dhlRates = await this.getDHLRates(data);
        if (dhlRates && dhlRates.length > 0) {
          rates = rates.concat(dhlRates);
        }
      }

      // Try FedEx API
      if (this.FEDEX_API_KEY && this.FEDEX_SECRET_KEY) {
        const fedexRates = await this.getFedExRates(data);
        if (fedexRates && fedexRates.length > 0) {
          rates = rates.concat(fedexRates);
        }
      }

      // If no rates found, use fallback
      if (rates.length === 0) {
        return this.calculateFallbackRates(data);
      }

      // Remove duplicates and sort by price
      const uniqueRates = this.removeDuplicateRates(rates);
      const sortedRates = uniqueRates.sort((a, b) => a.cost - b.cost);

      return successResponse("Shipping rates calculated successfully", {
        rates: sortedRates.slice(0, 8), // Return top 8 options
        fromAddress: data.fromCity + ', ' + data.fromCountry,
        toAddress: data.toCity + ', ' + data.toCountry,
        weight: data.weight,
        sources: this.getActiveSources()
      });
    } catch (error) {
      console.error('Calculate shipping rates error:', error);
      return this.calculateFallbackRates(data);
    }
  }

  /**
   * Get shipping rates from Shippo API
   */
  private async getShippoRates(data: ShippingCalculationData): Promise<ShippingRate[]> {
    try {

      // Create address objects for Shippo
      const fromAddress = {
        name: "King Collectibles",
        street1: "123 Business St",
        city: data.fromCity,
        state: "",
        zip: data.fromPostalCode,
        country: data.fromCountry,
        phone: "****** 123 4567",
        email: "<EMAIL>"
      };

      const toAddress = {
        name: "Customer",
        street1: "123 Customer St",
        city: data.toCity,
        state: "",
        zip: data.toPostalCode,
        country: data.toCountry,
        phone: "****** 987 6543",
        email: "<EMAIL>"
      };

      // Create parcel object
      const parcel = {
        length: data.dimensions?.length || 10,
        width: data.dimensions?.width || 10,
        height: data.dimensions?.height || 5,
        distance_unit: "cm",
        weight: data.weight,
        mass_unit: "kg"
      };

      // Create shipment
      const shipmentData = {
        address_from: fromAddress,
        address_to: toAddress,
        parcels: [parcel],
        async: false
      };

      const response = await fetch(`${this.SHIPPO_BASE_URL}/shipments/`, {
        method: 'POST',
        headers: {
          'Authorization': `ShippoToken ${this.SHIPPO_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(shipmentData)
      });

      if (!response.ok) {
        console.error('Shippo API error:', await response.text());
        return [];
      }

      const shipment = await response.json();

      if (!shipment.rates || shipment.rates.length === 0) {
        return [];
      }

      // Transform Shippo rates to our format
      const rates: ShippingRate[] = shipment.rates.map((rate: any) => ({
        carrier: rate.provider,
        service: rate.servicelevel.name,
        cost: parseFloat(rate.amount),
        currency: rate.currency,
        estimatedDays: rate.estimated_days || 7,
        trackingAvailable: true
      }));

      return rates;

    } catch (error) {
      console.error('Shippo API error:', error);
      return [];
    }
  }

  /**
   * Fallback shipping calculation when API is not available
   */
  private calculateFallbackRates(data: ShippingCalculationData): any {
    const baseRates = this.getBaseShippingRates(data.fromCountry, data.toCountry);
    const weightMultiplier = Math.max(1, Math.ceil(data.weight));
    
    const rates: ShippingRate[] = [
      {
        carrier: "International Standard",
        service: "Standard Shipping",
        cost: baseRates.standard * weightMultiplier,
        currency: "USD",
        estimatedDays: 14,
        trackingAvailable: true
      },
      {
        carrier: "International Express",
        service: "Express Shipping", 
        cost: baseRates.express * weightMultiplier,
        currency: "USD",
        estimatedDays: 7,
        trackingAvailable: true
      },
      {
        carrier: "International Priority",
        service: "Priority Shipping",
        cost: baseRates.priority * weightMultiplier,
        currency: "USD", 
        estimatedDays: 3,
        trackingAvailable: true
      }
    ];

    return successResponse("Shipping rates calculated (fallback)", {
      rates,
      fromAddress: data.fromCity + ', ' + data.fromCountry,
      toAddress: data.toCity + ', ' + data.toCountry,
      weight: data.weight,
      note: "Rates calculated using fallback method"
    });
  }

  /**
   * Get base shipping rates between countries
   */
  private getBaseShippingRates(fromCountry: string, toCountry: string) {
    // Base rates in USD
    const domesticRates = { standard: 5, express: 15, priority: 25 };
    const internationalRates = { standard: 25, express: 45, priority: 75 };
    const regionalRates = { standard: 15, express: 30, priority: 50 };

    // Same country
    if (fromCountry === toCountry) {
      return domesticRates;
    }

    // Regional shipping (same continent/region)
    const regions = {
      'US': ['CA', 'MX'],
      'GB': ['FR', 'DE', 'IT', 'ES', 'NL', 'BE'],
      'ID': ['MY', 'SG', 'TH', 'PH', 'VN'],
      'AU': ['NZ'],
      'JP': ['KR', 'CN', 'TW']
    };

    for (const [country, neighbors] of Object.entries(regions)) {
      if ((fromCountry === country && neighbors.includes(toCountry)) ||
          (toCountry === country && neighbors.includes(fromCountry))) {
        return regionalRates;
      }
    }

    return internationalRates;
  }

  /**
   * Get shipping options for checkout using address object
   */
  async getShippingOptionsFromAddress(shippingAddress: any, cartItems: any[]): Promise<any> {
    console.log("Getting shipping options for address:", shippingAddress, "with items:", cartItems.length);
    try {
      // Calculate total weight from cart items
      const totalWeight = cartItems.reduce((total, item) => {
        // Assume each item weighs 0.5kg if not specified
        const itemWeight = item.product?.weight || 0.5;
        return total + (itemWeight * item.quantity);
      }, 0);

      // Calculate shipping from warehouse location
      const shippingData: ShippingCalculationData = {
        fromCountry: 'US', // Warehouse country
        fromCity: 'New York',
        fromPostalCode: '10001',
        toCountry: shippingAddress.country,
        toCity: shippingAddress.city,
        toPostalCode: shippingAddress.zipCode || '',
        weight: Math.max(totalWeight, 0.1) // Minimum 0.1kg
      };

      return await this.calculateShippingRates(shippingData);

    } catch (error) {
      console.error('Get shipping options error:', error);
      return errorResponse("Failed to get shipping options");
    }
  }

  /**
   * Get shipping options for checkout (legacy method)
   */
  async getShippingOptions(shippingAddressId: string, cartItems: any[]): Promise<any> {
    console.log("Getting shipping options for address:", shippingAddressId, "with items:", cartItems.length);
    try {
      // Get shipping address
      const shippingAddress = await prisma.shippingAddress.findUnique({
        where: { id: shippingAddressId }
      });

      if (!shippingAddress) {
        return errorResponse("Shipping address not found");
      }

      return await this.getShippingOptionsFromAddress(shippingAddress, cartItems);

    } catch (error) {
      console.error('Get shipping options error:', error);
      return errorResponse("Failed to get shipping options");
    }
  }

  /**
   * Track shipment
   */
  async trackShipment(trackingNumber: string): Promise<any> {
    try {
      if (!this.SHIPPO_API_KEY) {
        return errorResponse("Tracking service not available");
      }

      const response = await fetch(`${this.SHIPPO_BASE_URL}/tracks/${trackingNumber}`, {
        headers: {
          'Authorization': `ShippoToken ${this.SHIPPO_API_KEY}`,
        }
      });

      if (!response.ok) {
        return errorResponse("Failed to track shipment");
      }

      const tracking = await response.json();
      
      return successResponse("Tracking information retrieved", {
        trackingNumber,
        status: tracking.tracking_status?.status || 'unknown',
        location: tracking.tracking_status?.location || 'N/A',
        estimatedDelivery: tracking.eta,
        trackingHistory: tracking.tracking_history || []
      });

    } catch (error) {
      console.error('Track shipment error:', error);
      return errorResponse("Failed to track shipment");
    }
  }

  /**
   * Get shipping rates from DHL API
   */
  private async getDHLRates(data: ShippingCalculationData): Promise<ShippingRate[]> {
    try {
      // DHL Express API for international shipping
      const requestData = {
        customerDetails: {
          shipperDetails: {
            postalCode: data.fromPostalCode,
            cityName: data.fromCity,
            countryCode: data.fromCountry,
          },
          receiverDetails: {
            postalCode: data.toPostalCode,
            cityName: data.toCity,
            countryCode: data.toCountry,
          }
        },
        accounts: [{
          typeCode: "shipper",
          number: "*********"
        }],
        productCode: "P",
        localProductCode: "P",
        valueAddedServices: [],
        productsAndServices: [{
          productCode: "P",
          localProductCode: "P"
        }],
        payerCountryCode: data.fromCountry,
        plannedShippingDateAndTime: new Date().toISOString(),
        unitOfMeasurement: "metric",
        isCustomsDeclarable: data.fromCountry !== data.toCountry,
        monetaryAmount: [{
          typeCode: "declared",
          value: 100,
          currency: "USD"
        }],
        requestAllValueAddedServices: false,
        returnStandardProductsOnly: false,
        nextBusinessDay: false,
        productTypeCode: "all",
        packages: [{
          typeCode: "3BX",
          weight: data.weight,
          dimensions: {
            length: data.dimensions?.length || 10,
            width: data.dimensions?.width || 10,
            height: data.dimensions?.height || 5
          }
        }]
      };

      const response = await fetch(`${this.DHL_BASE_URL}/mydhlapi/rates`, {
        method: 'POST',
        headers: {
          'DHL-API-Key': this.DHL_API_KEY!,
          'Content-Type': 'application/json',
          'Message-Reference': `RATE_${Date.now()}`,
          'Message-Reference-Date': new Date().toISOString().split('T')[0],
          'Plugin-Name': 'King Collectibles',
          'Plugin-Version': '1.0',
          'Shipping-System-Platform-Name': 'Node.js',
          'Shipping-System-Platform-Version': '18.0',
          'Webstore-Platform-Name': 'Custom',
          'Webstore-Platform-Version': '1.0'
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        console.error('DHL API error:', response.status, await response.text());
        return [];
      }

      const result = await response.json();

      if (!result.products || result.products.length === 0) {
        return [];
      }

      return result.products.map((product: any) => ({
        carrier: 'DHL',
        service: product.productName || 'DHL Express',
        cost: parseFloat(product.totalPrice?.[0]?.price || '0'),
        currency: product.totalPrice?.[0]?.currencyType || 'USD',
        estimatedDays: this.parseDHLDeliveryTime(product.deliveryCapabilities?.deliveryTypeCode),
        trackingAvailable: true
      }));

    } catch (error) {
      console.error('DHL API error:', error);
      return [];
    }
  }

  /**
   * Get shipping rates from FedEx API
   */
  private async getFedExRates(data: ShippingCalculationData): Promise<ShippingRate[]> {
    try {
      // First, get OAuth token
      const tokenResponse = await fetch(`${this.FEDEX_BASE_URL}/oauth/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: this.FEDEX_API_KEY!,
          client_secret: this.FEDEX_SECRET_KEY!
        })
      });

      if (!tokenResponse.ok) {
        console.error('FedEx token error:', tokenResponse.status);
        return [];
      }

      const tokenData = await tokenResponse.json();
      const accessToken = tokenData.access_token;

      // Now get rates
      const rateRequest = {
        accountNumber: {
          value: "*********"
        },
        requestedShipment: {
          shipper: {
            address: {
              postalCode: data.fromPostalCode,
              city: data.fromCity,
              countryCode: data.fromCountry
            }
          },
          recipient: {
            address: {
              postalCode: data.toPostalCode,
              city: data.toCity,
              countryCode: data.toCountry
            }
          },
          shipDateStamp: new Date().toISOString().split('T')[0],
          serviceType: "FEDEX_GROUND",
          packagingType: "YOUR_PACKAGING",
          pickupType: "USE_SCHEDULED_PICKUP",
          requestedPackageLineItems: [{
            weight: {
              units: "KG",
              value: data.weight
            },
            dimensions: {
              length: data.dimensions?.length || 10,
              width: data.dimensions?.width || 10,
              height: data.dimensions?.height || 5,
              units: "CM"
            }
          }],
          rateRequestType: ["ACCOUNT", "LIST"]
        }
      };

      const rateResponse = await fetch(`${this.FEDEX_BASE_URL}/rate/v1/rates/quotes`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          'X-locale': 'en_US'
        },
        body: JSON.stringify(rateRequest)
      });

      if (!rateResponse.ok) {
        console.error('FedEx rate error:', rateResponse.status, await rateResponse.text());
        return [];
      }

      const rateData = await rateResponse.json();

      if (!rateData.output?.rateReplyDetails || rateData.output.rateReplyDetails.length === 0) {
        return [];
      }

      return rateData.output.rateReplyDetails.map((rate: any) => ({
        carrier: 'FedEx',
        service: rate.serviceName || 'FedEx Service',
        cost: parseFloat(rate.ratedShipmentDetails?.[0]?.totalNetCharge || '0'),
        currency: rate.ratedShipmentDetails?.[0]?.currency || 'USD',
        estimatedDays: this.parseFedExDeliveryTime(rate.operationalDetail?.transitTime),
        trackingAvailable: true
      }));

    } catch (error) {
      console.error('FedEx API error:', error);
      return [];
    }
  }

  /**
   * Remove duplicate shipping rates
   */
  private removeDuplicateRates(rates: ShippingRate[]): ShippingRate[] {
    const seen = new Set<string>();
    return rates.filter(rate => {
      const key = `${rate.carrier}-${rate.service}-${rate.cost}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Get list of active shipping sources
   */
  private getActiveSources(): string[] {
    const sources = [];
    if (this.SHIPPO_API_KEY) sources.push('Shippo');
    if (this.DHL_API_KEY && this.DHL_API_SECRET) sources.push('DHL');
    if (this.FEDEX_API_KEY && this.FEDEX_SECRET_KEY) sources.push('FedEx');
    return sources;
  }

  /**
   * Parse DHL delivery time
   */
  private parseDHLDeliveryTime(deliveryType: string): number {
    switch (deliveryType) {
      case 'QDDC': return 1; // Same day
      case 'QDDF': return 1; // Next day
      case 'QDDM': return 2; // 2 days
      case 'QDDT': return 3; // 3 days
      default: return 5; // Default 5 days
    }
  }

  /**
   * Parse FedEx delivery time
   */
  private parseFedExDeliveryTime(transitTime: string): number {
    if (!transitTime) return 5;

    const match = transitTime.match(/(\d+)/);
    return match ? parseInt(match[1]) : 5;
  }
}

const shippingService = new ShippingService();
export default shippingService;
