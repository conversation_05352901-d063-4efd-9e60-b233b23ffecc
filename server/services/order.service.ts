import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";
import { OrdersQuery, UpdateOrderStatusData } from "../schemas/order.schema";

class OrderService {
  // Get user's orders with pagination and filtering
  async getUserOrders(userId: string, query: OrdersQuery) {
    try {
      const { page, limit, status, paymentStatus, sortBy, sortOrder } = query;
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = { userId };
      if (status) where.status = status;
      if (paymentStatus) where.paymentStatus = paymentStatus;

      // Get orders with pagination
      const [orders, total] = await Promise.all([
        prisma.order.findMany({
          where,
          include: {
            items: {
              include: {
                product: {
                  include: {
                    images: {
                      orderBy: { sortOrder: 'asc' }
                    }
                  }
                }
              }
            },
            shippingAddress: true
          },
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: limit,
        }),
        prisma.order.count({ where })
      ]);

      // Transform data to match schema
      const transformedOrders = orders.map(order => ({
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        createdAt: order.createdAt.toISOString(),
        updatedAt: order.updatedAt.toISOString(),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          createdAt: item.createdAt.toISOString(),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
            images: item.product.images.map(img => ({
              ...img,
              altText: img.altText || null,
            }))
          }
        }))
      }));

      const totalPages = Math.ceil(total / limit);

      return successResponse("Orders retrieved successfully", {
        orders: transformedOrders,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        }
      });
    } catch (error) {
      console.error("Get user orders error:", error);
      return errorResponse("Failed to retrieve orders");
    }
  }

  // Get single order by ID
  async getOrderById(userId: string, orderId: string) {
    try {
      const order = await prisma.order.findFirst({
        where: {
          id: orderId,
          userId, // Ensure user can only access their own orders
        },
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: {
                    orderBy: { sortOrder: 'asc' }
                  }
                }
              }
            }
          },
          shippingAddress: true,
          payment: true
        }
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      // Transform data to match schema
      const transformedOrder = {
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        createdAt: order.createdAt.toISOString(),
        updatedAt: order.updatedAt.toISOString(),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          createdAt: item.createdAt.toISOString(),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
            images: item.product.images.map(img => ({
              ...img,
              altText: img.altText || null,
            }))
          }
        }))
      };

      return successResponse("Order retrieved successfully", transformedOrder);
    } catch (error) {
      console.error("Get order by ID error:", error);
      return errorResponse("Failed to retrieve order");
    }
  }

  // Update order status (admin only)
  async updateOrderStatus(orderId: string, updateData: UpdateOrderStatusData) {
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId }
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      const updatedOrder = await prisma.order.update({
        where: { id: orderId },
        data: updateData,
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: {
                    orderBy: { sortOrder: 'asc' }
                  }
                }
              }
            }
          },
          shippingAddress: true
        }
      });

      // Transform data to match schema
      const transformedOrder = {
        ...updatedOrder,
        subtotal: Number(updatedOrder.subtotal),
        shippingCost: Number(updatedOrder.shippingCost),
        tax: Number(updatedOrder.tax),
        total: Number(updatedOrder.total),
        createdAt: updatedOrder.createdAt.toISOString(),
        updatedAt: updatedOrder.updatedAt.toISOString(),
        items: updatedOrder.items.map(item => ({
          ...item,
          price: Number(item.price),
          createdAt: item.createdAt.toISOString(),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
            images: item.product.images.map(img => ({
              ...img,
              altText: img.altText || null,
            }))
          }
        }))
      };

      return successResponse("Order status updated successfully", transformedOrder);
    } catch (error) {
      console.error("Update order status error:", error);
      return errorResponse("Failed to update order status");
    }
  }
}

export default new OrderService();
