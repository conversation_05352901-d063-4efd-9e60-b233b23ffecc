import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";

interface PaymentRedirectData {
  orderId: string;
  paymentMethod: string;
  paymentChannel?: string;
  customerInfo: {
    name: string;
    email: string;
    phone?: string;
  };
  successUrl: string;
  failureUrl: string;
}

class PaymentRedirectService {
  /**
   * Create payment and get appropriate redirect URL based on payment method
   */
  async createPaymentAndRedirect(data: PaymentRedirectData) {
    try {
      const { orderId, paymentMethod, paymentChannel, customerInfo, successUrl, failureUrl } = data;

      // Get order details
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          items: {
            include: {
              product: true
            }
          }
        }
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      const amount = Number(order.total);
      const currency = order.currency || 'USD';

      let redirectUrl = '';
      let paymentInstructions = {};

      switch (paymentMethod) {
        case 'xendit_invoice':
          const invoiceResult = await this.createXenditInvoice({
            orderId,
            amount,
            currency,
            customerInfo,
            successUrl,
            failureUrl
          });
          
          if (invoiceResult.status && invoiceResult.data) {
            redirectUrl = invoiceResult.data.invoiceUrl;
            paymentInstructions = {
              type: 'redirect',
              url: redirectUrl,
              message: 'Redirecting to Xendit payment page...'
            };
          }
          break;

        case 'ewallet':
          const ewalletResult = await this.createEwalletPayment({
            orderId,
            amount,
            currency,
            ewalletType: paymentChannel || 'OVO',
            customerInfo,
            successUrl,
            failureUrl
          });

          if (ewalletResult.status && ewalletResult.data) {
            const actions = ewalletResult.data.actions;
            if (actions?.mobile_deeplink_checkout_url) {
              redirectUrl = actions.mobile_deeplink_checkout_url;
              paymentInstructions = {
                type: 'deeplink',
                url: redirectUrl,
                qrCode: actions.qr_checkout_string,
                message: `Opening ${paymentChannel} app...`
              };
            } else if (actions?.qr_checkout_string) {
              paymentInstructions = {
                type: 'qr',
                qrCode: actions.qr_checkout_string,
                message: `Scan QR code with ${paymentChannel} app`
              };
            }
          }
          break;

        case 'virtual_account':
          const vaResult = await this.createVirtualAccount({
            orderId,
            amount,
            currency,
            bankCode: paymentChannel || 'BCA',
            customerInfo
          });

          if (vaResult.status && vaResult.data) {
            paymentInstructions = {
              type: 'virtual_account',
              bankCode: vaResult.data.bankCode,
              accountNumber: vaResult.data.accountNumber,
              amount: amount,
              message: `Transfer to ${paymentChannel} Virtual Account`
            };
          }
          break;

        case 'retail_outlet':
          const retailResult = await this.createRetailOutlet({
            orderId,
            amount,
            currency,
            retailOutlet: paymentChannel || 'ALFAMART',
            customerInfo
          });

          if (retailResult.status && retailResult.data) {
            paymentInstructions = {
              type: 'retail_outlet',
              paymentCode: retailResult.data.paymentCode,
              retailOutlet: paymentChannel,
              amount: amount,
              message: `Pay at ${paymentChannel} with payment code`
            };
          }
          break;

        default:
          return errorResponse("Unsupported payment method");
      }

      return successResponse("Payment created successfully", {
        orderId,
        paymentMethod,
        paymentChannel,
        redirectUrl,
        paymentInstructions,
        amount,
        currency
      });

    } catch (error) {
      console.error('Create payment and redirect error:', error);
      return errorResponse("Failed to create payment");
    }
  }

  /**
   * Create Xendit invoice
   */
  private async createXenditInvoice(data: any) {
    const XENDIT_BASE_URL = 'https://api.xendit.co';
    const XENDIT_SECRET_KEY = process.env.XENDIT_SECRET_KEY;

    if (!XENDIT_SECRET_KEY) {
      return errorResponse("Payment service not configured");
    }

    try {
      const externalId = `invoice_${data.orderId}_${Date.now()}`;
      
      const invoiceData = {
        external_id: externalId,
        amount: Math.round(data.amount),
        currency: data.currency,
        customer: {
          given_names: data.customerInfo.name,
          email: data.customerInfo.email,
        },
        description: `Payment for Order #${data.orderId}`,
        invoice_duration: 86400, // 24 hours
        success_redirect_url: data.successUrl,
        failure_redirect_url: data.failureUrl,
      };

      const response = await fetch(`${XENDIT_BASE_URL}/v2/invoices`, {
        method: "POST",
        headers: {
          'Authorization': `Basic ${Buffer.from(XENDIT_SECRET_KEY + ':').toString('base64')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invoiceData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Xendit invoice creation failed:", errorData);
        return errorResponse("Failed to create invoice");
      }

      const xenditInvoice = await response.json();

      // Check if payment already exists and delete if not paid
      const existingPayment = await prisma.payment.findFirst({
        where: { orderId: data.orderId },
      });

      if (existingPayment) {
        if (existingPayment.status === "PAID") {
          return errorResponse("Order already paid");
        }

        // Delete existing payment if not paid
        if (existingPayment.status === "PENDING" || existingPayment.status === "EXPIRED" || existingPayment.status === "FAILED") {
          await prisma.payment.delete({
            where: { id: existingPayment.id }
          });
        }
      }

      // Save payment record
      await prisma.payment.create({
        data: {
          orderId: data.orderId,
          xenditPaymentId: xenditInvoice.id,
          externalId: externalId,
          status: "PENDING",
          amount: data.amount,
          currency: data.currency,
          paymentMethod: "xendit_invoice",
          invoiceUrl: xenditInvoice.invoice_url,
          expiryDate: new Date(xenditInvoice.expiry_date),
        },
      });

      return successResponse("Invoice created successfully", {
        invoiceUrl: xenditInvoice.invoice_url,
        expiryDate: xenditInvoice.expiry_date
      });

    } catch (error) {
      console.error('Create Xendit invoice error:', error);
      return errorResponse("Failed to create invoice");
    }
  }

  /**
   * Create e-wallet payment
   */
  private async createEwalletPayment(data: any) {
    // Implementation for e-wallet payment
    // This would use Xendit's e-wallet API
    return successResponse("E-wallet payment created", {
      actions: {
        mobile_deeplink_checkout_url: `https://app.${data.ewalletType.toLowerCase()}.com/payment/${data.orderId}`,
        qr_checkout_string: `qr_code_for_${data.orderId}`
      }
    });
  }

  /**
   * Create virtual account payment
   */
  private async createVirtualAccount(data: any) {
    // Implementation for virtual account payment
    return successResponse("Virtual account created", {
      bankCode: data.bankCode,
      accountNumber: `8808${Math.random().toString().substr(2, 8)}`,
      amount: data.amount
    });
  }

  /**
   * Create retail outlet payment
   */
  private async createRetailOutlet(data: any) {
    // Implementation for retail outlet payment
    return successResponse("Retail outlet payment created", {
      paymentCode: Math.random().toString().substr(2, 8).toUpperCase(),
      retailOutlet: data.retailOutlet,
      amount: data.amount
    });
  }

  /**
   * Get payment status and redirect info
   */
  async getPaymentStatus(orderId: string) {
    try {
      const payment = await prisma.payment.findFirst({
        where: { orderId },
        include: {
          order: true
        }
      });

      if (!payment) {
        return errorResponse("Payment not found");
      }

      return successResponse("Payment status retrieved", {
        status: payment.status,
        paymentMethod: payment.paymentMethod,
        amount: Number(payment.amount),
        currency: payment.currency,
        invoiceUrl: payment.invoiceUrl,
        expiryDate: payment.expiryDate
      });

    } catch (error) {
      console.error('Get payment status error:', error);
      return errorResponse("Failed to get payment status");
    }
  }
}

const paymentRedirectService = new PaymentRedirectService();
export default paymentRedirectService;
