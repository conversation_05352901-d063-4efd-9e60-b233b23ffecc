import bcrypt from "bcryptjs";
import { prisma } from "../db";
import { RegisterInput } from "../types/auth";
import { errorResponse, successResponse } from "../utils/response.util";
import { sign, verify } from 'hono/jwt';
import crypto from 'crypto';
import { OAuth2Client } from 'google-auth-library';

class AuthService {
  private generateRefreshToken(): string {
    return crypto.randomBytes(64).toString('hex');
  }

  private async generateTokens(user: any) {
    const accessTokenPayload = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      exp: Math.floor(Date.now() / 1000) + (5000 * 60), // 5000 minutes (approximately 3.47 days)
    };

    const refreshTokenPayload = {
      id: user.id,
      type: 'refresh',
      exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7 days
    };

    const secret = process.env.JWT_SECRET || 'keyyyyy';
    const accessToken = await sign(accessTokenPayload, secret);
    const refreshToken = await sign(refreshTokenPayload, secret);

    return { accessToken, refreshToken };
  }

  async login(body: { email: string; password: string }) {
    try {
      const { email, password } = body;

      const user = await prisma.user.findFirst({
        where: {
          OR: [
            { email: email },
            { phoneNumber: email },
          ],
        },
      });

      const isPasswordValid = await bcrypt.compare(password, user?.password || "");

      if (!user || !isPasswordValid) {
        return errorResponse("Email or Phone number does not exist");
      }

      const { accessToken, refreshToken } = await this.generateTokens(user);

      await prisma.user.update({
        where: { id: user.id },
        data: { refreshToken }
      });

      return successResponse("Login successful", {
        accessToken,
        refreshToken,
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
        },
        expiresAt: Math.floor(Date.now() / 1000) + (5000 * 60), // 5000 minutes (approximately 3.47 days)
      });
    } catch (error) {
      throw new Error(`Login failed: ${(error as Error).message}`);
    }
  }

  async register(body: RegisterInput) {
    try {
      const {
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        confirmPassword
      } = body;

      const userExists = await prisma.user.findFirst({
        where: {
          OR: [
            { email: email },
            { phoneNumber: phoneNumber },
          ],
        },
      });

      if (userExists) {
        return errorResponse("User already exists", {
          email: ["User with this email already exists"],
          phoneNumber: ["User with this phone number already exists"],
        });
      }

      if (password !== confirmPassword) {
        return errorResponse("Passwords do not match", {
          password: ["Passwords do not match"],
          confirmPassword: ["Passwords do not match"],
        });
      }

      const hashedPassword = await bcrypt.hash(password, 10)

      const newUser = await prisma.user.create({
        data: {
          firstName,
          lastName,
          email,
          phoneNumber,
          password: hashedPassword,
        },
      });

      if (!newUser) {
        return errorResponse("Failed to create user");
      }

      return successResponse("Register successfully")
    } catch (error) {
      throw new Error(`Report generation failed: ${(error as Error).message}`);
    }
  }

  async getProfile(userId: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phoneNumber: true,
          createdAt: true,
          updatedAt: true,
        }
      });

      if (!user) {
        return errorResponse("User not found");
      }

      return successResponse("Profile retrieved successfully", user);
    } catch (error) {
      throw new Error(`Profile retrieval failed: ${(error as Error).message}`);
    }
  }

  async refreshToken(refreshToken: string) {
    try {
      const secret = process.env.JWT_SECRET || 'keyyyyy';

      const payload = await verify(refreshToken, secret) as any;

      if (payload.type !== 'refresh') {
        return errorResponse("Invalid refresh token");
      }

      const user = await prisma.user.findFirst({
        where: {
          id: payload.id,
          refreshToken: refreshToken
        }
      });

      if (!user) {
        return errorResponse("Invalid refresh token");
      }

      const { accessToken, refreshToken: newRefreshToken } = await this.generateTokens(user);

      await prisma.user.update({
        where: { id: user.id },
        data: { refreshToken: newRefreshToken }
      });

      return successResponse("Token refreshed successfully", {
        accessToken,
        refreshToken: newRefreshToken,
        expiresAt: Math.floor(Date.now() / 1000) + (5000 * 60), // 5000 minutes (approximately 3.47 days)
      });
    } catch (error) {
      return errorResponse("Invalid or expired refresh token");
    }
  }

  async googleAuth(googleToken: string) {
    try {
      const googleUser = await this.verifyGoogleToken(googleToken);

      if (!googleUser) {
        return errorResponse("Invalid Google token");
      }

      let user = await prisma.user.findUnique({
        where: { email: googleUser.email }
      });

      console.log("Google User:", googleUser);

      if (!user) {
        user = await prisma.user.create({
          data: {
            firstName: googleUser.given_name || '',
            lastName: googleUser.family_name || '',
            email: googleUser.email,
            phoneNumber: '',
            password: '', 
            googleId: googleUser.sub,
          }
        });
      } else {
        if (!user.googleId) {
          user = await prisma.user.update({
            where: { id: user.id },
            data: { googleId: googleUser.sub }
          });
        }
      }

      const { accessToken, refreshToken } = await this.generateTokens(user);

      await prisma.user.update({
        where: { id: user.id },
        data: { refreshToken }
      });

      return successResponse("Google authentication successful", {
        accessToken,
        refreshToken,
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
        },
        expiresAt: Math.floor(Date.now() / 1000) + (5000 * 60), // 5000 minutes (approximately 3.47 days)
      });
    } catch (error) {
      console.error('Google authentication error:', error);
      throw new Error(`Google authentication failed: ${(error as Error).message}`);
    }
  }

  private async verifyGoogleToken(googleToken: string): Promise<any> {
    try {
      const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

      const ticket = await client.verifyIdToken({
        idToken: googleToken,
        audience: process.env.GOOGLE_CLIENT_ID,
      });

      const payload = ticket.getPayload();

      if (!payload) {
        throw new Error('Invalid Google token payload');
      }

      return {
        sub: payload.sub,
        email: payload.email,
        given_name: payload.given_name,
        family_name: payload.family_name,
        picture: payload.picture,
      };
    } catch (error) {
      console.error('Google token verification failed:', error);
      return null;
    }
  }
}

export default new AuthService();
