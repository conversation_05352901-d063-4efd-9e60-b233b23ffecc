import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";

class CartService {
  // Get user's cart
  async getCart(userId: string) {
    try {
      let cart = await prisma.cart.findUnique({
        where: { userId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: {
                    orderBy: { sortOrder: 'asc' }
                  }
                }
              }
            },
            orderBy: { createdAt: 'desc' }
          }
        }
      });

      // Create cart if it doesn't exist
      if (!cart) {
        cart = await prisma.cart.create({
          data: { userId },
          include: {
            items: {
              include: {
                product: {
                  include: {
                    images: {
                      orderBy: { sortOrder: 'asc' }
                    }
                  }
                }
              }
            }
          }
        });
      }

      // Calculate totals
      const totalItems = cart.items.reduce((sum, item) => sum + item.quantity, 0);
      const totalPrice = cart.items.reduce((sum, item) => sum + (Number(item.price) * item.quantity), 0);

      const cartResponse = {
        ...cart,
        totalItems,
        totalPrice,
        items: cart.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Cart retrieved successfully", cartResponse);
    } catch (error) {
      console.error("Get cart service error:", error);
      return errorResponse("Failed to retrieve cart");
    }
  }

  // Add item to cart
  async addToCart(userId: string, productId: string, quantity: number) {
    try {
      // Check if product exists and is available for purchase
      const carts = await prisma.cart.findUnique({
        where: { userId },
        include: {
          items: {
            where: { productId },
            include: {
              product: {
                include: {
                  images: {
                    orderBy: { sortOrder: 'asc' }
                  }
                }
              }
            }
          }
        }
      });

      if (carts && carts.items.length > 0) {
        return errorResponse("Item already exists in cart");
      }

      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: { images: true }
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      if (product.status !== 'active') {
        return errorResponse("Product is not available for purchase");
      }

      if (product.sellType !== 'buy-now') {
        return errorResponse("This product is only available through auction");
      }

      // Get or create cart
      let cart = await prisma.cart.findUnique({
        where: { userId }
      });

      if (!cart) {
        cart = await prisma.cart.create({
          data: { userId }
        });
      }

      // Check if item already exists in cart
      const existingItem = await prisma.cartItem.findUnique({
        where: {
          cartId_productId: {
            cartId: cart.id,
            productId: productId
          }
        }
      });

      let cartItem;
      if (existingItem) {
        // Update quantity
        cartItem = await prisma.cartItem.update({
          where: { id: existingItem.id },
          data: { 
            quantity: existingItem.quantity + quantity,
            price: product.priceUSD
          },
          include: {
            product: {
              include: {
                images: {
                  orderBy: { sortOrder: 'asc' }
                }
              }
            }
          }
        });
      } else {
        // Create new cart item
        cartItem = await prisma.cartItem.create({
          data: {
            cartId: cart.id,
            productId: productId,
            quantity: quantity,
            price: product.priceUSD
          },
          include: {
            product: {
              include: {
                images: {
                  orderBy: { sortOrder: 'asc' }
                }
              }
            }
          }
        });
      }

      const response = {
        ...cartItem,
        price: Number(cartItem.price),
        product: {
          ...cartItem.product,
          priceUSD: Number(cartItem.product.priceUSD),
          currentBid: cartItem.product.currentBid ? Number(cartItem.product.currentBid) : null,
        }
      };

      return successResponse("Item added to cart successfully", response);
    } catch (error) {
      console.error("Add to cart service error:", error);
      return errorResponse("Failed to add item to cart");
    }
  }

  // Update cart item quantity
  async updateCartItem(userId: string, itemId: string, quantity: number) {
    try {
      // Verify the cart item belongs to the user
      const cartItem = await prisma.cartItem.findFirst({
        where: {
          id: itemId,
          cart: { userId }
        },
        include: {
          product: {
            include: {
              images: {
                orderBy: { sortOrder: 'asc' }
              }
            }
          }
        }
      });

      if (!cartItem) {
        return errorResponse("Cart item not found");
      }

      const updatedItem = await prisma.cartItem.update({
        where: { id: itemId },
        data: { quantity },
        include: {
          product: {
            include: {
              images: {
                orderBy: { sortOrder: 'asc' }
              }
            }
          }
        }
      });

      const response = {
        ...updatedItem,
        price: Number(updatedItem.price),
        product: {
          ...updatedItem.product,
          priceUSD: Number(updatedItem.product.priceUSD),
          currentBid: updatedItem.product.currentBid ? Number(updatedItem.product.currentBid) : null,
        }
      };

      return successResponse("Cart item updated successfully", response);
    } catch (error) {
      console.error("Update cart item service error:", error);
      return errorResponse("Failed to update cart item");
    }
  }

  // Remove item from cart
  async removeFromCart(userId: string, itemId: string) {
    try {
      // Verify the cart item belongs to the user
      const cartItem = await prisma.cartItem.findFirst({
        where: {
          id: itemId,
          cart: { userId }
        }
      });

      if (!cartItem) {
        return errorResponse("Cart item not found");
      }

      await prisma.cartItem.delete({
        where: { id: itemId }
      });

      return successResponse("Item removed from cart successfully");
    } catch (error) {
      console.error("Remove from cart service error:", error);
      return errorResponse("Failed to remove item from cart");
    }
  }

  // Clear cart
  async clearCart(userId: string) {
    try {
      const cart = await prisma.cart.findUnique({
        where: { userId }
      });

      if (!cart) {
        return errorResponse("Cart not found");
      }

      await prisma.cartItem.deleteMany({
        where: { cartId: cart.id }
      });

      return successResponse("Cart cleared successfully");
    } catch (error) {
      console.error("Clear cart service error:", error);
      return errorResponse("Failed to clear cart");
    }
  }
}

export default new CartService();
