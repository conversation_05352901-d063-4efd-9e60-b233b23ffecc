import { WebSocket, WebSocketServer } from 'ws';

interface WebSocketClient {
  ws: WebSocket;
  userId?: string;
  productId?: string;
  subscriptions: Set<string>;
}

class WebSocketService {
  private wss: WebSocketServer | null = null;
  private clients: Map<string, WebSocketClient> = new Map();

  /**
   * Initialize WebSocket server
   */
  initialize(server: any) {
    this.wss = new WebSocketServer({ server });

    this.wss.on('connection', (ws: WebSocket, request) => {
      const clientId = this.generateClientId();
      const client: WebSocketClient = {
        ws,
        subscriptions: new Set()
      };

      this.clients.set(clientId, client);
      console.log(`WebSocket client connected: ${clientId}`);

      // Handle incoming messages
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(clientId, message);
        } catch (error) {
          console.error('WebSocket message parse error:', error);
        }
      });

      // Handle client disconnect
      ws.on('close', () => {
        this.clients.delete(clientId);
        console.log(`WebSocket client disconnected: ${clientId}`);
      });

      // Handle errors
      ws.on('error', (error) => {
        console.error(`WebSocket error for client ${clientId}:`, error);
        this.clients.delete(clientId);
      });

      // Send welcome message
      this.sendToClient(clientId, {
        type: 'connected',
        clientId,
        message: 'WebSocket connection established'
      });
    });

    console.log('WebSocket server initialized');
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(clientId: string, message: any) {
    const client = this.clients.get(clientId);
    if (!client) return;

    switch (message.type) {
      case 'subscribe':
        this.handleSubscribe(clientId, message);
        break;
      case 'unsubscribe':
        this.handleUnsubscribe(clientId, message);
        break;
      case 'authenticate':
        this.handleAuthenticate(clientId, message);
        break;
      default:
        console.log(`Unknown message type: ${message.type}`);
    }
  }

  /**
   * Handle subscription to channels
   */
  private handleSubscribe(clientId: string, message: any) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { channel } = message;
    if (channel) {
      client.subscriptions.add(channel);
      console.log(`Client ${clientId} subscribed to ${channel}`);
      
      this.sendToClient(clientId, {
        type: 'subscribed',
        channel,
        message: `Subscribed to ${channel}`
      });
    }
  }

  /**
   * Handle unsubscription from channels
   */
  private handleUnsubscribe(clientId: string, message: any) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { channel } = message;
    if (channel) {
      client.subscriptions.delete(channel);
      console.log(`Client ${clientId} unsubscribed from ${channel}`);
      
      this.sendToClient(clientId, {
        type: 'unsubscribed',
        channel,
        message: `Unsubscribed from ${channel}`
      });
    }
  }

  /**
   * Handle client authentication
   */
  private handleAuthenticate(clientId: string, message: any) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { userId, token } = message;
    
    // TODO: Validate token here
    if (userId) {
      client.userId = userId;
      console.log(`Client ${clientId} authenticated as user ${userId}`);
      
      this.sendToClient(clientId, {
        type: 'authenticated',
        userId,
        message: 'Authentication successful'
      });
    }
  }

  /**
   * Send message to specific client
   */
  private sendToClient(clientId: string, message: any) {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      client.ws.send(JSON.stringify(message));
    }
  }

  /**
   * Broadcast to all clients subscribed to a channel
   */
  public broadcast(channel: string, message: any) {
    const broadcastMessage = {
      type: 'broadcast',
      channel,
      data: message,
      timestamp: new Date().toISOString()
    };

    this.clients.forEach((client, clientId) => {
      if (client.subscriptions.has(channel) && client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(JSON.stringify(broadcastMessage));
      }
    });

    console.log(`Broadcasted to channel ${channel}: ${JSON.stringify(message)}`);
  }

  /**
   * Send message to specific user
   */
  public sendToUser(userId: string, message: any) {
    this.clients.forEach((client, clientId) => {
      if (client.userId === userId && client.ws.readyState === WebSocket.OPEN) {
        const userMessage = {
          type: 'user_message',
          data: message,
          timestamp: new Date().toISOString()
        };
        client.ws.send(JSON.stringify(userMessage));
      }
    });
  }

  /**
   * Notify about new bid
   */
  public notifyNewBid(productId: string, bidData: any) {
    this.broadcast(`product:${productId}`, {
      type: 'new_bid',
      productId,
      bid: bidData
    });
  }

  /**
   * Notify about auto-bid activation
   */
  public notifyAutoBidActivated(userId: string, productId: string, autoBidData: any) {
    this.sendToUser(userId, {
      type: 'auto_bid_activated',
      productId,
      autoBid: autoBidData
    });

    this.broadcast(`product:${productId}`, {
      type: 'auto_bid_status',
      productId,
      message: 'Auto-bid activated by a user'
    });
  }

  /**
   * Notify about auto-bid execution
   */
  public notifyAutoBidExecuted(userId: string, productId: string, bidData: any) {
    this.sendToUser(userId, {
      type: 'auto_bid_executed',
      productId,
      bid: bidData,
      message: 'Your auto-bid has been executed'
    });

    this.broadcast(`product:${productId}`, {
      type: 'auto_bid_executed',
      productId,
      bid: bidData
    });
  }

  /**
   * Notify about auction status changes
   */
  public notifyAuctionStatusChange(productId: string, status: string, data: any) {
    this.broadcast(`product:${productId}`, {
      type: 'auction_status_change',
      productId,
      status,
      data
    });
  }

  /**
   * Generate unique client ID
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get connection statistics
   */
  public getStats() {
    const totalClients = this.clients.size;
    const authenticatedClients = Array.from(this.clients.values()).filter(c => c.userId).length;
    const totalSubscriptions = Array.from(this.clients.values()).reduce((sum, c) => sum + c.subscriptions.size, 0);

    return {
      totalClients,
      authenticatedClients,
      totalSubscriptions,
      channels: this.getActiveChannels()
    };
  }

  /**
   * Get list of active channels
   */
  private getActiveChannels(): string[] {
    const channels = new Set<string>();
    this.clients.forEach(client => {
      client.subscriptions.forEach(channel => channels.add(channel));
    });
    return Array.from(channels);
  }

  /**
   * Close all connections
   */
  public close() {
    this.clients.forEach((client, clientId) => {
      client.ws.close();
    });
    this.clients.clear();
    
    if (this.wss) {
      this.wss.close();
    }
    
    console.log('WebSocket service closed');
  }
}

// Export singleton instance
const webSocketService = new WebSocketService();
export default webSocketService;
