import { PrismaClient } from "../../generated/client";
import { errorResponse, successResponse } from "../utils/response.util";
import crypto from "crypto";

// Create a dedicated Prisma instance for product pricing
const prisma = new PrismaClient({
  log: ['error', 'warn'],
  errorFormat: 'pretty',
});

interface ProductPriceConversion {
  productId: string;
  originalPrice: number;
  originalCurrency: string;
  targetCurrency: string;
  convertedPrice: number;
  exchangeRate: number;
  margin?: number;
}

class ProductPricingService {
  private readonly DEFAULT_MARGIN = 0.02; // 2% margin

  /**
   * Get current 6-hour period (0-3 for 4 periods in a day)
   */
  private getCurrentPeriod(): number {
    const now = new Date();
    const hour = now.getHours();

    if (hour >= 0 && hour < 6) return 0;      // 00:00 - 05:59
    if (hour >= 6 && hour < 12) return 1;     // 06:00 - 11:59
    if (hour >= 12 && hour < 18) return 2;    // 12:00 - 17:59
    return 3;                                 // 18:00 - 23:59
  }

  /**
   * Get period source identifier for database
   */
  private getPeriodSource(period: number): string {
    const sources = [
      'period_00_06', // 00:00 - 05:59
      'period_06_12', // 06:00 - 11:59
      'period_12_18', // 12:00 - 17:59
      'period_18_24'  // 18:00 - 23:59
    ];
    return sources[period];
  }

  /**
   * Get or fetch exchange rate for product pricing with 6-hour caching
   */
  async getExchangeRateForPricing(fromCurrency: string, toCurrency: string) {
    try {
      if (fromCurrency === toCurrency) {
        return { rate: 1, source: 'same_currency' };
      }

      const today = new Date();
      today.setUTCHours(0, 0, 0, 0);

      const currentPeriod = this.getCurrentPeriod();
      const periodSource = this.getPeriodSource(currentPeriod);

      // Check if we have rate for current 6-hour period
      const existingRate = await prisma.currencyRate.findFirst({
        where: {
          fromCurrency,
          toCurrency,
          date: today,
          source: periodSource,
          isActive: true
        }
      });

      if (existingRate) {
        console.log(`✅ Using cached rate for period ${currentPeriod} (${periodSource}): ${fromCurrency}/${toCurrency} = ${existingRate.rate}`);
        return {
          rate: Number(existingRate.rate),
          source: `database_${periodSource}`,
          date: existingRate.date,
          period: currentPeriod
        };
      }

      // If no rate exists for current period, fetch from API and save
      console.log(`📡 Fetching new rate for period ${currentPeriod} (${periodSource}): ${fromCurrency}/${toCurrency}`);
      const fetchedRate = await this.fetchAndSaveExchangeRateForPeriod(fromCurrency, toCurrency, currentPeriod, today);

      if (fetchedRate) {
        return {
          rate: fetchedRate.rate,
          source: `api_fresh_${periodSource}`,
          date: today,
          period: currentPeriod
        };
      }

      // Fallback to static rate
      return this.getFallbackRate(fromCurrency, toCurrency);

    } catch (error) {
      console.error('Get exchange rate error:', error);
      return this.getFallbackRate(fromCurrency, toCurrency);
    }
  }

  /**
   * Fetch and save exchange rate from external API for specific period
   */
  private async fetchAndSaveExchangeRateForPeriod(fromCurrency: string, toCurrency: string, period: number, date: Date) {
    const periodSource = this.getPeriodSource(period);
    return await this.fetchAndSaveExchangeRate(fromCurrency, toCurrency, periodSource, date);
  }

  /**
   * Fetch exchange rate from API and save to database
   */
  private async fetchAndSaveExchangeRate(fromCurrency: string, toCurrency: string, source: string = 'product_pricing_api', dateOverride?: Date) {
    try {
      // Only support USD-IDR conversion for now
      if (!((fromCurrency === 'USD' && toCurrency === 'IDR') || (fromCurrency === 'IDR' && toCurrency === 'USD'))) {
        console.log(`Unsupported currency pair: ${fromCurrency}/${toCurrency}`);
        return null;
      }

      // Fetch USD to IDR rate from API
      const usdToIdrRate = await this.fetchLiveUsdToIdrRate();
      if (!usdToIdrRate) {
        console.error('Failed to fetch live USD/IDR rate');
        return null;
      }

      const today = dateOverride ? new Date(dateOverride) : new Date();
      today.setUTCHours(0, 0, 0, 0);

      // Calculate rates with margin
      const usdToIdrSellRate = usdToIdrRate * (1 + this.DEFAULT_MARGIN);
      const usdToIdrBuyRate = usdToIdrRate * (1 - this.DEFAULT_MARGIN);
      
      const idrToUsdRate = 1 / usdToIdrRate;
      const idrToUsdSellRate = idrToUsdRate * (1 + this.DEFAULT_MARGIN);
      const idrToUsdBuyRate = idrToUsdRate * (1 - this.DEFAULT_MARGIN);

      // Save USD to IDR rate

      // Use safe create or update to avoid unique constraint error
      const usdToIdrSaved = await this.safeCreateOrUpdateCurrencyRate({
        fromCurrency: 'USD',
        toCurrency: 'IDR',
        rate: usdToIdrRate,
        sellRate: usdToIdrSellRate,
        buyRate: usdToIdrBuyRate,
        margin: this.DEFAULT_MARGIN,
        source: source,
        date: today
      });

      // Save IDR to USD rate using safe create or update
      const idrToUsdSaved = await this.safeCreateOrUpdateCurrencyRate({
        fromCurrency: 'IDR',
        toCurrency: 'USD',
        rate: idrToUsdRate,
        sellRate: idrToUsdSellRate,
        buyRate: idrToUsdBuyRate,
        margin: this.DEFAULT_MARGIN,
        source: source,
        date: today
      });

      if (!usdToIdrSaved || !idrToUsdSaved) {
        throw new Error('Failed to save exchange rates to database');
      }

      console.log(`✅ Saved exchange rates: USD/IDR = ${usdToIdrRate} (sell: ${usdToIdrSellRate})`);

      // Return the requested rate
      if (fromCurrency === 'USD' && toCurrency === 'IDR') {
        return {
          rate: usdToIdrRate,
          sellRate: usdToIdrSellRate,
          buyRate: usdToIdrBuyRate
        };
      } else {
        return {
          rate: idrToUsdRate,
          sellRate: idrToUsdSellRate,
          buyRate: idrToUsdBuyRate
        };
      }

    } catch (error) {
      console.error('Fetch and save exchange rate error:', error);
      return null;
    }
  }

  /**
   * Fetch live USD to IDR rate from external API
   */
  private async fetchLiveUsdToIdrRate(): Promise<number | null> {
    try {
      console.log('🌐 Fetching live USD/IDR rate from API...');
      
      // Try ExchangeRate-API first (free, reliable)
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
      
      if (response.ok) {
        const data = await response.json();
        if (data.rates && data.rates.IDR && !isNaN(data.rates.IDR)) {
          console.log(`📈 Live rate fetched: 1 USD = ${data.rates.IDR} IDR`);
          return data.rates.IDR;
        }
      }

      // Try alternative API if first fails
      const altResponse = await fetch('https://open.er-api.com/v6/latest/USD');
      if (altResponse.ok) {
        const altData = await altResponse.json();
        if (altData.rates && altData.rates.IDR && !isNaN(altData.rates.IDR)) {
          console.log(`📈 Live rate fetched (alt): 1 USD = ${altData.rates.IDR} IDR`);
          return altData.rates.IDR;
        }
      }

      console.error('❌ All exchange rate APIs failed');
      return null;

    } catch (error) {
      console.error('Fetch live rate error:', error);
      return null;
    }
  }

  /**
   * Get fallback rate for unsupported pairs or API failures
   */
  private getFallbackRate(fromCurrency: string, toCurrency: string) {
    const FALLBACK_RATES = {
      USD_TO_IDR: 15000,
      IDR_TO_USD: 1 / 15000,
    };

    let rate = 1;
    
    if (fromCurrency === 'USD' && toCurrency === 'IDR') {
      rate = FALLBACK_RATES.USD_TO_IDR * (1 + this.DEFAULT_MARGIN);
    } else if (fromCurrency === 'IDR' && toCurrency === 'USD') {
      rate = FALLBACK_RATES.IDR_TO_USD * (1 + this.DEFAULT_MARGIN);
    }

    console.log(`⚠️ Using fallback rate: ${fromCurrency}/${toCurrency} = ${rate}`);
    
    return {
      rate,
      source: 'fallback',
      date: new Date()
    };
  }

  /**
   * Convert product price with exchange rate
   */
  async convertProductPrice(price: number, fromCurrency: string, toCurrency: string): Promise<ProductPriceConversion> {
    try {
      // Validate inputs
      if (!price || isNaN(price) || price <= 0) {
        throw new Error('Invalid price provided');
      }

      if (!fromCurrency || !toCurrency) {
        throw new Error('Currency codes are required');
      }

      // Get exchange rate
      const rateInfo = await this.getExchangeRateForPricing(fromCurrency, toCurrency);
      
      if (!rateInfo.rate || isNaN(rateInfo.rate) || rateInfo.rate <= 0) {
        throw new Error('Invalid exchange rate');
      }

      // Calculate converted price
      const convertedPrice = price * rateInfo.rate;
      
      if (isNaN(convertedPrice)) {
        throw new Error('Price conversion resulted in NaN');
      }

      return {
        productId: '', // Will be set by caller
        originalPrice: price,
        originalCurrency: fromCurrency,
        targetCurrency: toCurrency,
        convertedPrice: Math.round(convertedPrice * 100) / 100, // Round to 2 decimal places
        exchangeRate: Math.round(rateInfo.rate * 10000) / 10000, // Round to 4 decimal places
        margin: this.DEFAULT_MARGIN
      };

    } catch (error) {
      console.error('Convert product price error:', error);
      throw error;
    }
  }

  /**
   * Get products with converted prices
   */
  async getProductsWithConvertedPrices(targetCurrency: string = 'IDR', page: number = 1, limit: number = 20) {
    try {
      const skip = (page - 1) * limit;

      const products = await prisma.product.findMany({
        include: {
          images: {
            take: 1,
            orderBy: { sortOrder: 'asc' }
          },
          category: true,
          seller: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      });

      const productsWithConvertedPrices = await Promise.all(
        products.map(async (product) => {
          try {
            // Convert price from USD to target currency
            const originalPriceUSD = Number(product.priceUSD);
            let convertedPrice = originalPriceUSD;
            let exchangeRate = 1;

            if (targetCurrency !== 'USD') {
              const conversion = await this.convertProductPrice(
                originalPriceUSD,
                'USD',
                targetCurrency
              );
              convertedPrice = conversion.convertedPrice;
              exchangeRate = conversion.exchangeRate;
            }

            return {
              ...product,
              originalPrice: originalPriceUSD,
              originalCurrency: 'USD',
              convertedPrice,
              displayCurrency: targetCurrency,
              exchangeRate,
              priceUSD: originalPriceUSD // Keep original USD price
            };
          } catch (error) {
            console.error(`Error converting price for product ${product.id}:`, error);
            // Return original product if conversion fails
            return {
              ...product,
              originalPrice: Number(product.priceUSD),
              originalCurrency: 'USD',
              convertedPrice: Number(product.priceUSD),
              displayCurrency: 'USD',
              exchangeRate: 1
            };
          }
        })
      );

      return successResponse("Products with converted prices retrieved successfully", {
        products: productsWithConvertedPrices,
        pagination: {
          page,
          limit,
          total: await prisma.product.count()
        },
        targetCurrency
      });

    } catch (error) {
      console.error('Get products with converted prices error:', error);
      return errorResponse("Failed to get products with converted prices");
    }
  }

  /**
   * Initialize exchange rates (run this once to setup)
   */
  async initializeExchangeRates() {
    try {
      console.log('🚀 Initializing exchange rates...');
      
      // Fetch and save USD/IDR rates
      const result = await this.fetchAndSaveExchangeRate('USD', 'IDR');
      
      if (result) {
        return successResponse("Exchange rates initialized successfully", {
          usdToIdr: result.sellRate,
          source: 'api',
          date: new Date().toISOString()
        });
      } else {
        // Create fallback rates
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        const fallbackRate = 15000;
        const sellRate = fallbackRate * (1 + this.DEFAULT_MARGIN);
        const buyRate = fallbackRate * (1 - this.DEFAULT_MARGIN);



        // Use upsert for fallback rate
        await prisma.currencyRate.upsert({
          where: {
            fromCurrency_toCurrency_date: {
              fromCurrency: 'USD',
              toCurrency: 'IDR',
              date: today
            }
          },
          update: {
            rate: fallbackRate,
            sellRate,
            buyRate,
            margin: this.DEFAULT_MARGIN,
            source: 'fallback_init',
            isActive: true,
            updatedAt: new Date()
          },
          create: {
            fromCurrency: 'USD',
            toCurrency: 'IDR',
            rate: fallbackRate,
            sellRate,
            buyRate,
            margin: this.DEFAULT_MARGIN,
            source: 'fallback_init',
            date: today,
            isActive: true
          }
        });

        return successResponse("Exchange rates initialized with fallback", {
          usdToIdr: sellRate,
          source: 'fallback',
          date: today.toISOString()
        });
      }

    } catch (error) {
      console.error('Initialize exchange rates error:', error);
      return errorResponse("Failed to initialize exchange rates");
    }
  }

  /**
   * Safe create or update currency rate using raw SQL with ON DUPLICATE KEY UPDATE
   */
  private async safeCreateOrUpdateCurrencyRate(data: {
    fromCurrency: string;
    toCurrency: string;
    rate: number;
    sellRate: number;
    buyRate: number;
    margin: number;
    source: string;
    date: Date;
  }): Promise<boolean> {
    try {
      // Use raw SQL with ON DUPLICATE KEY UPDATE to handle unique constraint
      const dateStr = data.date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
      const now = new Date().toISOString().replace('T', ' ').replace('Z', ''); // Format for MySQL
      const uuid = crypto.randomUUID();

      await prisma.$executeRaw`
        INSERT INTO CurrencyRate (
          id, fromCurrency, toCurrency, rate, sellRate, buyRate,
          margin, source, date, isActive, createdAt, updatedAt
        ) VALUES (
          ${uuid}, ${data.fromCurrency}, ${data.toCurrency}, ${data.rate},
          ${data.sellRate}, ${data.buyRate}, ${data.margin}, ${data.source},
          ${dateStr}, true, ${now}, ${now}
        )
        ON DUPLICATE KEY UPDATE
          rate = VALUES(rate),
          sellRate = VALUES(sellRate),
          buyRate = VALUES(buyRate),
          margin = VALUES(margin),
          source = VALUES(source),
          isActive = VALUES(isActive),
          updatedAt = VALUES(updatedAt)
      `;

      return true;
    } catch (error: any) {
      console.error('Failed to create/update currency rate with raw SQL:', error);
      return false;
    }
  }
}

const productPricingService = new ProductPricingService();
export default productPricingService;
