// Simple API test script
const BASE_URL = 'http://localhost:3000/api/v1';

async function testAPI() {
  console.log('🧪 Testing API endpoints...\n');

  try {
    // Test 1: Get Categories (Master API)
    console.log('1. Testing GET /master/categories');
    const categoriesResponse = await fetch(`${BASE_URL}/master/categories`);
    const categoriesResult = await categoriesResponse.json();
    console.log('Status:', categoriesResponse.status);
    console.log('Categories count:', categoriesResult.data?.length || 0);
    if (categoriesResult.data?.length > 0) {
      console.log('First category:', categoriesResult.data[0].name);
    }
    console.log('✅ Categories endpoint working\n');

    // Test 2: Get Item Types (Master API)
    console.log('2. Testing GET /master/item-types');
    const itemTypesResponse = await fetch(`${BASE_URL}/master/item-types`);
    const itemTypesResult = await itemTypesResponse.json();
    console.log('Status:', itemTypesResponse.status);
    console.log('Item types count:', itemTypesResult.data?.length || 0);
    if (itemTypesResult.data?.length > 0) {
      console.log('First item type:', itemTypesResult.data[0].name);
    }
    console.log('✅ Item types endpoint working\n');

    // Test 3: Get Item Types by Category
    if (categoriesResult.data?.length > 0) {
      const firstCategoryId = categoriesResult.data[0].id;
      console.log('3. Testing GET /master/item-types?categoryId=' + firstCategoryId);
      const filteredItemTypesResponse = await fetch(`${BASE_URL}/master/item-types?categoryId=${firstCategoryId}`);
      const filteredItemTypesResult = await filteredItemTypesResponse.json();
      console.log('Status:', filteredItemTypesResponse.status);
      console.log('Filtered item types count:', filteredItemTypesResult.data?.length || 0);
      console.log('✅ Filtered item types endpoint working\n');
    }

    // Test 4: Get Products
    console.log('4. Testing GET /products');
    const productsResponse = await fetch(`${BASE_URL}/products`);
    const productsResult = await productsResponse.json();
    console.log('Status:', productsResponse.status);
    console.log('Products count:', productsResult.data?.products?.length || 0);
    console.log('✅ Products endpoint working\n');

    console.log('🎉 All API endpoints are working correctly!');

  } catch (error) {
    console.error('❌ Error testing API:', error);
  }
}

// Run the test
testAPI();
