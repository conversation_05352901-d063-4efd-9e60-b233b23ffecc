#!/bin/bash

# King Collectibles Development Server Startup Script

echo "🚀 Starting King Collectibles Development Environment..."

# Check if .env.local exists
if [ ! -f .env.local ]; then
    echo "⚠️  .env.local not found. Creating from .env.example..."
    cp .env.example .env.local
    echo "✅ Created .env.local. Please configure your environment variables."
    echo "📝 Edit .env.local with your database URL, Google OAuth credentials, etc."
    exit 1
fi

# Check if node_modules exists
if [ ! -d node_modules ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Check if Prisma client is generated
if [ ! -d generated/client ]; then
    echo "🔧 Generating Prisma client..."
    npx prisma generate
fi

# Check database connection
echo "🔍 Checking database connection..."
npx prisma db push --accept-data-loss 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Database connected successfully"
else
    echo "❌ Database connection failed. Please check your DATABASE_URL in .env.local"
    echo "💡 Make sure your MySQL server is running"
    exit 1
fi

# Run database migrations
echo "🔄 Running database migrations..."
npx prisma migrate deploy

# Start the development server
echo "🌟 Starting Next.js development server..."
echo "📱 Frontend: http://localhost:3000"
echo "🔐 Auth endpoints: http://localhost:3000/api/auth"
echo "📊 API docs: http://localhost:3001/api/v1/docs (if backend is running)"
echo ""
echo "🔑 Google OAuth Setup:"
echo "   1. Go to Google Cloud Console"
echo "   2. Add redirect URI: http://localhost:3000/api/auth/callback/google"
echo "   3. Update GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in .env.local"
echo ""
echo "Press Ctrl+C to stop the server"
echo "----------------------------------------"

npm run dev
