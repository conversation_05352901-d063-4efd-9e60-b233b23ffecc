'use client'

import { useEffect, useRef, useCallback } from 'react'
import { useQueryClient } from '@tanstack/react-query'

interface WebSocketMessage {
  type: string
  channel?: string
  data: any
  timestamp: string
}

interface UseWebSocketOptions {
  productId?: string
  userId?: string
  onMessage?: (message: WebSocketMessage) => void
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Event) => void
}

export const useWebSocket = (options: UseWebSocketOptions = {}) => {
  const { productId, userId, onMessage, onConnect, onDisconnect, onError } = options
  const wsRef = useRef<WebSocket | null>(null)
  const queryClient = useQueryClient()
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>()
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    try {
      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'
      wsRef.current = new WebSocket(wsUrl)

      wsRef.current.onopen = () => {
        console.log('WebSocket connected')
        reconnectAttempts.current = 0
        onConnect?.()

        // Subscribe to product updates if productId is provided
        if (productId) {
          wsRef.current?.send(JSON.stringify({
            type: 'subscribe',
            channel: `product:${productId}`
          }))
        }

        // Subscribe to user updates if userId is provided
        if (userId) {
          wsRef.current?.send(JSON.stringify({
            type: 'subscribe',
            channel: `user:${userId}`
          }))
        }
      }

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          console.log('WebSocket message received:', message)

          // Handle different message types
          switch (message.type) {
            case 'broadcast':
              if (message.channel?.startsWith('product:') && message.data.type === 'new_bid') {
                // Invalidate product queries for real-time bid updates
                const productIdFromChannel = message.channel.split(':')[1]
                queryClient.invalidateQueries({
                  queryKey: ['products', 'detail', productIdFromChannel]
                })
                queryClient.invalidateQueries({
                  queryKey: ['products', 'slug']
                })
                queryClient.invalidateQueries({
                  queryKey: ['bidding', 'history', productIdFromChannel]
                })
              }
              break

            case 'user_message':
              if (message.data.type === 'auto_bid_executed') {
                // Invalidate queries when auto-bid is executed
                const productIdFromMessage = message.data.productId
                queryClient.invalidateQueries({
                  queryKey: ['products', 'detail', productIdFromMessage]
                })
                queryClient.invalidateQueries({
                  queryKey: ['products', 'slug']
                })
                queryClient.invalidateQueries({
                  queryKey: ['bidding', 'history', productIdFromMessage]
                })
                queryClient.invalidateQueries({
                  queryKey: ['auto-bid', productIdFromMessage]
                })
              }
              break
          }

          onMessage?.(message)
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

      wsRef.current.onclose = () => {
        console.log('WebSocket disconnected')
        onDisconnect?.()

        // Attempt to reconnect
        if (reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000)
          console.log(`Attempting to reconnect in ${delay}ms (attempt ${reconnectAttempts.current})`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, delay)
        }
      }

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error)
        onError?.(error)
      }
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
    }
  }, [productId, userId, onConnect, onDisconnect, onMessage, onError, queryClient])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }
    
    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }
  }, [])

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }, [])

  useEffect(() => {
    connect()

    return () => {
      disconnect()
    }
  }, [connect, disconnect])

  return {
    isConnected: wsRef.current?.readyState === WebSocket.OPEN,
    sendMessage,
    connect,
    disconnect
  }
}

// Hook specifically for product real-time updates
export const useProductWebSocket = (productId: string) => {
  const queryClient = useQueryClient()

  return useWebSocket({
    productId,
    onMessage: (message) => {
      // Additional product-specific message handling can be added here
      console.log('Product WebSocket message:', message)
    }
  })
}

// Hook specifically for user real-time updates
export const useUserWebSocket = (userId: string) => {
  return useWebSocket({
    userId,
    onMessage: (message) => {
      // Additional user-specific message handling can be added here
      console.log('User WebSocket message:', message)
    }
  })
}
