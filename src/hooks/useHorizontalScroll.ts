'use client'

import { useRef, useState, useEffect, useCallback } from 'react'

interface UseHorizontalScrollOptions {
  scrollAmount?: number
  dragMultiplier?: number
}

export const useHorizontalScroll = (
  options: UseHorizontalScrollOptions = {}
) => {
  const { scrollAmount = 300, dragMultiplier = 2 } = options

  const containerRef = useRef<HTMLDivElement>(null)

  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [scrollLeft, setScrollLeft] = useState(0)
  const [showLeftButton, setShowLeftButton] = useState(false)
  const [showRightButton, setShowRightButton] = useState(true)

  const updateButtonVisibility = useCallback(() => {
    const container = containerRef.current
    if (!container) return

    const { scrollLeft, scrollWidth, clientWidth } = container
    setShowLeftButton(scrollLeft > 0)
    setShowRightButton(scrollLeft < scrollWidth - clientWidth)
  }, [])

  // Scroll with button
  const scroll = useCallback(
    (direction: 'left' | 'right') => {
      const container = containerRef.current
      if (!container) return

      const scrollValue = direction === 'left' ? -scrollAmount : scrollAmount
      container.scrollBy({ left: scrollValue, behavior: 'smooth' })

      // Delay update until scroll finishes
      setTimeout(updateButtonVisibility, 300)
    },
    [scrollAmount, updateButtonVisibility]
  )

  // Mouse events
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!containerRef.current) return
    setIsDragging(true)
    setStartX(e.pageX - containerRef.current.offsetLeft)
    setScrollLeft(containerRef.current.scrollLeft)
  }, [])

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!isDragging || !containerRef.current) return
      e.preventDefault()
      const x = e.pageX - containerRef.current.offsetLeft
      const walk = (x - startX) * dragMultiplier
      containerRef.current.scrollLeft = scrollLeft - walk
    },
    [isDragging, startX, scrollLeft, dragMultiplier]
  )

  const handleMouseUp = useCallback(() => setIsDragging(false), [])
  const handleMouseLeave = useCallback(() => setIsDragging(false), [])

  // Touch events
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!containerRef.current) return
    setIsDragging(true)
    setStartX(e.touches[0].pageX - containerRef.current.offsetLeft)
    setScrollLeft(containerRef.current.scrollLeft)
  }, [])

  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (!isDragging || !containerRef.current) return
      const x = e.touches[0].pageX - containerRef.current.offsetLeft
      const walk = (x - startX) * dragMultiplier
      containerRef.current.scrollLeft = scrollLeft - walk
    },
    [isDragging, startX, scrollLeft, dragMultiplier]
  )

  const handleTouchEnd = useCallback(() => setIsDragging(false), [])

  // Attach scroll event
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const handleScroll = () => {
      requestAnimationFrame(updateButtonVisibility)
    }

    container.addEventListener('scroll', handleScroll)
    updateButtonVisibility()

    return () => {
      container.removeEventListener('scroll', handleScroll)
    }
  }, [updateButtonVisibility])

  return {
    containerRef,
    isDragging,
    showLeftButton,
    showRightButton,
    scroll,
    handlers: {
      onMouseDown: handleMouseDown,
      onMouseMove: handleMouseMove,
      onMouseUp: handleMouseUp,
      onMouseLeave: handleMouseLeave,
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd,
    },
  }
}
