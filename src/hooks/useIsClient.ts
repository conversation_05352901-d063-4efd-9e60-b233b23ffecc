import { useState, useEffect } from 'react';

/**
 * Hook to detect if code is running on client side
 * Useful for preventing hydration mismatches
 */
// export const useIsClient = (): boolean => {
//   const [isClient, setIsClient] = useState(false);

//   useEffect(() => {
//     setIsClient(true);
//   }, []);

//   return isClient;
// };

/**
 * Hook to safely use browser-only APIs
 * Returns undefined on server, actual value on client
 */
// export const useSafeValue = <T>(getValue: () => T, fallback?: T): T | undefined => {
//   const isClient = useIsClient();
//   const [value, setValue] = useState<T | undefined>(fallback);

//   useEffect(() => {
//     if (isClient) {
//       setValue(getValue());
//     }
//   }, [isClient, getValue]);

//   return isClient ? value : fallback;
// };

/**
 * Hook for safe date formatting to prevent hydration mismatches
 */
// export const useSafeDate = (date: string | Date | null, formatter: (date: Date) => string): string | null => {
//   const isClient = useIsClient();
//   const [formattedDate, setFormattedDate] = useState<string | null>(null);

//   useEffect(() => {
//     if (isClient && date) {
//       try {
//         const dateObj = typeof date === 'string' ? new Date(date) : date;
//         setFormattedDate(formatter(dateObj));
//       } catch (error) {
//         console.error('Error formatting date:', error);
//         setFormattedDate(null);
//       }
//     }
//   }, [isClient, date, formatter]);

//   return isClient ? formattedDate : null;
// };
