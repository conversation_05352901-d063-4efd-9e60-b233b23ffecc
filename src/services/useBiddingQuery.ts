import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";
import { toaster } from "@/components/ui/toaster";

// Types
export interface Bid {
  id: string;
  productId: string;
  bidderId: string;
  amount: number; // Changed to number for easier calculations
  isWinning: boolean;
  createdAt: string; // ISO date string
  product?: {
    id: string;
    itemName: string;
    slug?: string;
    currentBid?: number; // Optional, may not be present in all responses
    auctionEndDate?: string; // Optional, may not be present in all responses
  };
}

export interface BidHistory {
  productId: string;
  bids: Bid[];
  totalBids: number;
  highestBid: number;
  currentWinner?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export interface PlaceBidData {
  productId: string;
  bidAmount: number;
  bidType?: 'manual' | 'auto';
  maxBudget?: number;
  bidIncrement?: number;
}

export interface UserBidSummary {
  productId: string;
  product: {
    id: string;
    itemName: string;
    slug?: string;
    currentBid?: number;
    auctionEndDate?: string;
    status: string;
    images: Array<{
      id: string;
      imageUrl: string;
      isMain: boolean;
    }>;
  };
  highestBid: number;
  totalBids: number;
  isWinning: boolean;
  lastBidTime: string;
  auctionStatus: 'active' | 'ended' | 'won' | 'lost';
}

export interface UserBidsListResponse {
  bids: UserBidSummary[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UserBidsQueryParams {
  page?: number;
  limit?: number;
  status?: 'active' | 'ended' | 'won' | 'lost';
  sortBy?: 'createdAt' | 'amount' | 'auctionEndDate';
  sortOrder?: 'asc' | 'desc';
}

// Query Keys
export const biddingQueryKeys = {
  all: ['bidding'] as const,
  bidHistory: (productId: string) => [...biddingQueryKeys.all, 'history', productId] as const,
  userBids: () => [...biddingQueryKeys.all, 'user-bids'] as const,
  userBidsList: (params: UserBidsQueryParams) => [...biddingQueryKeys.userBids(), params] as const,
  userBid: (productId: string) => [...biddingQueryKeys.all, 'user-bid', productId] as const,
  bidDetail: (bidId: string) => [...biddingQueryKeys.all, 'bid-detail', bidId] as const,
};

// Get Bid History for a Product
export const useBidHistoryQuery = (productId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: biddingQueryKeys.bidHistory(productId),
    queryFn: async (): Promise<Bid[]> => {
      console.log('Fetching bid history for product:', productId);
      const response = await apiClient.get(`/bidding/history/${productId}`);
      console.log('Bid history response:', response.data);
      return response.data?.data || response.data || [];
    },
    enabled: !!productId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for live updates
  });
};

// Get User's Bids Summary
// export const useUserBidsQuery = () => {
//   const apiClient = useAuthenticatedApi();

//   return useQuery({
//     queryKey: biddingQueryKeys.userBids(),
//     queryFn: async (): Promise<UserBidSummary[]> => {
//       const response = await apiClient.get('/user/bids');
//       return response.data;
//     },
//     staleTime: 2 * 60 * 1000, // 2 minutes
//   });
// };

// Get User's Bidding History
export const useUserBidsQuery = (params: UserBidsQueryParams = {}) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: biddingQueryKeys.userBidsList(params),
    queryFn: async (): Promise<UserBidsListResponse> => {
      const searchParams = new URLSearchParams();

      if (params.page) searchParams.append('page', params.page.toString());
      if (params.limit) searchParams.append('limit', params.limit.toString());
      if (params.status) searchParams.append('status', params.status);
      if (params.sortBy) searchParams.append('sortBy', params.sortBy);
      if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

      const response = await apiClient.get(`/bidding?${searchParams.toString()}`);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get User's Highest Bid for a Product
export const useUserBidQuery = (productId: string, userId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: biddingQueryKeys.userBid(productId),
    queryFn: async (): Promise<Bid | null> => {
      const response = await apiClient.get(`/products/${productId}/user-bid`);
      return response.data;
    },
    enabled: !!productId && !!userId,
    staleTime: 30 * 1000, // 30 seconds
  });
};

// Get Bid Detail
export const useBidDetailQuery = (bidId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: biddingQueryKeys.bidDetail(bidId),
    queryFn: async (): Promise<Bid> => {
      const response = await apiClient.get(`/bidding/${bidId}`);
      return response.data;
    },
    enabled: !!bidId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Place Bid Mutation
export const usePlaceBidMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: PlaceBidData): Promise<Bid> => {
      const response = await apiClient.post(`/products/${data.productId}/bids`, {
        productId: data.productId,
        bidAmount: data.bidAmount
      });
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ 
        queryKey: biddingQueryKeys.bidHistory(variables.productId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: biddingQueryKeys.userBid(variables.productId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: biddingQueryKeys.userBids() 
      });
      
      // Also invalidate product queries to update current bid
      queryClient.invalidateQueries({ 
        queryKey: ['products', 'detail', variables.productId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['products', 'slug'] 
      });
      
      toaster.create({
        title: "Bid Placed Successfully",
        description: `Your bid of $${data.amount} has been placed.`,
        type: "success",
      });
    },
    onError: (error, variables) => {
      const errorMessage = error instanceof Error ? error.message : "Failed to place bid";
      
      toaster.create({
        title: "Bid Failed",
        description: errorMessage,
        type: "error",
      });
      
      console.error('Failed to place bid:', error);
    },
  });
};

// Auto-bid Mutation
export const useAutoBidMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      productId: string;
      startingBid: number;
      maxBudget: number;
      bidIncrement: number;
    }): Promise<any> => {
      console.log('Enabling auto-bid with data:', data);
      const response = await apiClient.post(`/auto-bid/enable`, {
        productId: data.productId,
        startingBid: data.startingBid,
        maxBudget: data.maxBudget,
        bidIncrement: data.bidIncrement
      });
      console.log('Auto-bid response:', response.data);
      return response.data;
    },
    onSuccess: (_, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: biddingQueryKeys.bidHistory(variables.productId)
      });
      queryClient.invalidateQueries({
        queryKey: biddingQueryKeys.userBids()
      });
      queryClient.invalidateQueries({
        queryKey: ['auto-bid', variables.productId]
      });

      // Invalidate product queries to refresh current bid
      queryClient.invalidateQueries({
        queryKey: ['products', 'detail', variables.productId]
      });
      queryClient.invalidateQueries({
        queryKey: ['products', 'slug']
      });

      toaster.create({
        title: "Auto-bid Enabled",
        description: `Auto-bidding enabled with max budget of $${variables.maxBudget}.`,
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Auto-bid Failed",
        description: error instanceof Error ? error.message : "Failed to enable auto-bidding",
        type: "error",
      });
    },
  });
};

// Cancel Auto-bid Mutation
export const useCancelAutoBidMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (productId: string): Promise<any> => {
      console.log('Disabling auto-bid for product:', productId);
      const response = await apiClient.delete(`/auto-bid/${productId}`);
      console.log('Cancel auto-bid response:', response.data);
      return response.data;
    },
    onSuccess: (_, productId) => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: biddingQueryKeys.userBids()
      });
      queryClient.invalidateQueries({
        queryKey: ['auto-bid', productId]
      });

      toaster.create({
        title: "Auto-bid Cancelled",
        description: "Auto-bidding has been disabled for this product.",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to cancel auto-bidding",
        type: "error",
      });
    },
  });
};

// Get User Auto-bid for Product
export const useUserAutoBidQuery = (productId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: ['auto-bid', productId],
    queryFn: async (): Promise<any> => {
      const response = await apiClient.get(`/auto-bid/${productId}`);
      return response.data;
    },
    enabled: !!productId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get All User Auto-bids
export const useUserAutoBidsQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: ['auto-bid', 'user-list'],
    queryFn: async (): Promise<any[]> => {
      const response = await apiClient.get('/auto-bid');
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Auto-bid Statistics
export const useAutoBidStatsQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: ['auto-bid', 'stats'],
    queryFn: async (): Promise<any> => {
      const response = await apiClient.get('/auto-bid/stats/summary');
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
