import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";
import { toaster } from "@/components/ui/toaster";
import { useRouter } from "next/navigation";

// Types
export interface Category {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  _count: {
    products: number;
    itemTypes: number;
  };
}

export interface ItemType {
  id: string;
  name: string;
  description?: string;
  categoryId?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  category?: {
    id: string;
    name: string;
  };
  _count: {
    products: number;
  };
}

export interface ProductImage {
  id: string;
  productId: string;
  imageUrl: string;
  altText?: string;
  sortOrder: number;
  isMain: boolean;
  createdAt: string;
}

export interface Product {
  id: string;
  itemName: string;
  slug?: string;
  description?: string;
  sellType: 'auction' | 'buy-now';
  priceUSD: number;
  auctionStartDate?: string;
  auctionEndDate?: string;
  currentBid?: number;
  bidCount: number;
  extendedBiddingEnabled: boolean;
  extendedBiddingMinutes?: number;
  extendedBiddingDuration?: number;
  status: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  sellerId: string;
  categoryId: string;
  itemTypeId: string;
  seller: {
    id: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  };
  category: Category;
  itemType: ItemType;
  images: ProductImage[];
  bids?: Array<{
    id: string;
    amount: number;
    createdAt: string;
    bidder: {
      id: string;
      firstName: string;
      lastName: string;
    };
  }>;
  _count?: {
    bids: number;
  };
  auctionStatus?: 'active' | 'ended' | 'upcoming';
  timeLeft: string; // Added timeLeft for auction products
}

export interface ProductsResponse {
  products: Product[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export type SellType = 'auction' | 'buy-now' | 'all' | '';
export type SortField = 'createdAt' | 'priceUSD' | 'auctionEndDate' | 'bidCount';
export type SortOrder = 'asc' | 'desc';

export interface ProductQueryParams {
  sellerId?: string; 
  page?: number;
  limit?: number;
  sellType?: SellType;
  category?: string;
  categoryId?: string;
  status?: string;
  search?: string;
  sortBy?: SortField;
  sortOrder?: SortOrder;
  minPrice?: number;
  maxPrice?: number;
  keyword?: string;
  itemTypeId?: string;
}

export interface PriceRangeState {
  min: string;
  max: string;
}

export interface CreateProductData {
  itemName: string;
  slug?: string;
  description?: string;
  sellType: 'auction' | 'buy-now';
  priceUSD: number;
  categoryId: string;
  itemTypeId: string;
  auctionStartDate?: string;
  auctionEndDate?: string;
  extendedBiddingEnabled?: boolean;
  extendedBiddingMinutes?: number;
  extendedBiddingDuration?: number;
  images: {
    imageUrl: string;
    altText?: string;
    sortOrder: number;
    isMain: boolean;
  }[];
}

// Query Keys
export const productQueryKeys = {
  all: ['products'] as const,
  lists: () => [...productQueryKeys.all, 'list'] as const,
  list: (params: ProductQueryParams) => [...productQueryKeys.lists(), params] as const,
  details: () => [...productQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...productQueryKeys.details(), id] as const,
  categories: ['categories'] as const,
  itemTypes: ['itemTypes'] as const,
  itemTypesByCategory: (categoryId: string) => [...productQueryKeys.itemTypes, categoryId] as const,
};

// Categories Query
export const useCategoriesQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: productQueryKeys.categories,
    queryFn: async (): Promise<Category[]> => {
      const response = await apiClient.get('/master/categories');
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

// Item Types Query
export const useItemTypesQuery = (categoryId?: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: categoryId
      ? productQueryKeys.itemTypesByCategory(categoryId)
      : productQueryKeys.itemTypes,
    queryFn: async (): Promise<ItemType[]> => {
      const url = categoryId
        ? `/master/item-types?categoryId=${categoryId}`
        : '/master/item-types';
      const response = await apiClient.get(url);
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

// Products Query
export const useProductsQuery = (params: ProductQueryParams = {}) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: productQueryKeys.list(params),
    queryFn: async (): Promise<ProductsResponse> => {
      const searchParams = new URLSearchParams();

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await apiClient.get(`/products?${searchParams.toString()}`);
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
};

// Single Product Query
export const useProductQuery = (id: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: productQueryKeys.detail(id),
    queryFn: async (): Promise<Product> => {
      const response = await apiClient.get(`/products/${id}`);
      return response.data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Product by Slug Query
export const useProductBySlugQuery = (slug: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: [...productQueryKeys.all, 'slug', slug],
    queryFn: async (): Promise<Product> => {
      const response = await apiClient.get(`/products/slug/${slug}`);
      return response.data;
    },
    enabled: !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Product by ID Query
export const useProductByIdQuery = (id: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: productQueryKeys.detail(id),
    queryFn: async (): Promise<Product> => {
      const response = await apiClient.get(`/products/${id}`);
      return response.data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Create Product Mutation
export const useCreateProductMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: async (data: CreateProductData): Promise<Product> => {
      const response = await apiClient.post('/products', data);
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate and refetch products list
      queryClient.invalidateQueries({ queryKey: productQueryKeys.lists() });

      toaster.create({
        title: "Success",
        description: "Product created successfully",
        type: "success",
      });

      router.push(`/account/selling`); // Redirect to product detail page
    },
    onError: (error: Error) => {
      toaster.create({
        title: "Error",
        description: error.message || "Failed to create product",
        type: "error",
      });
    },
  });
};

// Update Product Mutation
export const useUpdateProductMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<CreateProductData> }): Promise<Product> => {
      const response = await apiClient.put(`/products/${id}`, data);
      return response.data;
    },
    onSuccess: (data) => {
      // Update the specific product in cache
      queryClient.setQueryData(productQueryKeys.detail(data.id), data);

      // Invalidate products list
      queryClient.invalidateQueries({ queryKey: productQueryKeys.lists() });

      toaster.create({
        title: "Success",
        description: "Product updated successfully",
        type: "success",
      });
    },
    onError: (error: Error) => {
      toaster.create({
        title: "Error",
        description: error.message || "Failed to update product",
        type: "error",
      });
    },
  });
};

// Delete Product Mutation
export const useDeleteProductMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      await apiClient.delete(`/products/${id}`);
    },
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: productQueryKeys.detail(id) });

      // Invalidate products list
      queryClient.invalidateQueries({ queryKey: productQueryKeys.lists() });

      toaster.create({
        title: "Success",
        description: "Product deleted successfully",
        type: "success",
      });
    },
    onError: (error: Error) => {
      toaster.create({
        title: "Error",
        description: error.message || "Failed to delete product",
        type: "error",
      });
    },
  });
};

// Upload Images Mutation
export const useUploadImagesMutation = () => {
  const apiClient = useAuthenticatedApi();

  return useMutation({
    mutationFn: async (files: File[]): Promise<{ urls: string[]; count: number }> => {
      const formData = new FormData();
      files.forEach((file) => {
        formData.append('images', file);
      });

      const response = await apiClient.post('/products/upload-images', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    },
    onSuccess: (data) => {
      toaster.create({
        title: "Success",
        description: `${data.count} image(s) uploaded successfully`,
        type: "success",
      });
    },
    onError: (error: Error) => {
      toaster.create({
        title: "Error",
        description: error.message || "Failed to upload images",
        type: "error",
      });
    },
  });
};
