import { useQuery } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";

// Types
export interface BidHistoryItem {
  id: string;
  amount: number;
  currency: string;
  bidTime: string;
  bidder: {
    id: string;
    name: string;
    email: string;
    profileImage?: string;
    displayEmail?: string;
  };
  isWinning: boolean;
  bidNumber: number;
}

export interface BidStats {
  totalBids: number;
  uniqueBidders: number;
  currentHighestBid: number;
  averageBid: number;
  bidRange: {
    lowest: number;
    highest: number;
  };
}

export interface BidHistoryResponse {
  bidHistory: BidHistoryItem[];
  stats: BidStats;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface UserBidHistoryItem {
  id: string;
  amount: number;
  currency: string;
  bidTime: string;
  isWinning: boolean;
  isOutbid: boolean;
  bidNumber: number;
}

export interface UserBidHistoryResponse {
  userBids: UserBidHistoryItem[];
  totalUserBids: number;
  highestUserBid: number;
  isCurrentWinner: boolean;
}

export interface BidActivityItem {
  id: string;
  amount: number;
  bidderName: string;
  timeAgo: string;
  bidTime: string;
}

export interface BidActivityResponse {
  recentActivity: BidActivityItem[];
  last24Hours: {
    totalBids: number;
    uniqueBidders: number;
    highestBid: number;
  };
}

// Query Keys
export const bidHistoryQueryKeys = {
  all: ['bidHistory'] as const,
  product: (productId: string) => [...bidHistoryQueryKeys.all, 'product', productId] as const,
  productHistory: (productId: string, page?: number, limit?: number) => 
    [...bidHistoryQueryKeys.product(productId), 'history', { page, limit }] as const,
  userHistory: (productId: string) => [...bidHistoryQueryKeys.product(productId), 'user'] as const,
  activity: (productId: string) => [...bidHistoryQueryKeys.product(productId), 'activity'] as const,
};

// Get bid history for a product
export const useBidHistoryQuery = (productId: string, page: number = 1, limit: number = 20) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: bidHistoryQueryKeys.productHistory(productId, page, limit),
    queryFn: async (): Promise<BidHistoryResponse> => {
      const response = await apiClient.get(`/bid-history/product/${productId}`, {
        params: { page: page.toString(), limit: limit.toString() }
      });
      return response.data;
    },
    enabled: !!productId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute for real-time updates
  });
};

// Get user's bid history for a product
export const useUserBidHistoryQuery = (productId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: bidHistoryQueryKeys.userHistory(productId),
    queryFn: async (): Promise<UserBidHistoryResponse> => {
      const response = await apiClient.get(`/bid-history/product/${productId}/user`);
      return response.data;
    },
    enabled: !!productId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
  });
};

// Get bid activity summary for a product
export const useBidActivityQuery = (productId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: bidHistoryQueryKeys.activity(productId),
    queryFn: async (): Promise<BidActivityResponse> => {
      const response = await apiClient.get(`/bid-history/product/${productId}/activity`);
      return response.data;
    },
    enabled: !!productId,
    staleTime: 15 * 1000, // 15 seconds for activity
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for real-time activity
  });
};

// Hook for real-time bid updates
export const useRealTimeBidHistory = (productId: string, enabled: boolean = true) => {
  const bidHistoryQuery = useBidHistoryQuery(productId, 1, 10);
  const activityQuery = useBidActivityQuery(productId);

  return {
    bidHistory: bidHistoryQuery.data,
    activity: activityQuery.data,
    isLoading: bidHistoryQuery.isLoading || activityQuery.isLoading,
    error: bidHistoryQuery.error || activityQuery.error,
    refetch: () => {
      bidHistoryQuery.refetch();
      activityQuery.refetch();
    }
  };
};
