import React from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";

// Types
export interface RelatedProduct {
  id: string;
  itemName: string;
  slug: string;
  priceUSD: number;
  currentBid?: number;
  bidCount: number;
  sellType: 'auction' | 'buy-now';
  status: string;
  auctionEndDate?: string;
  images: Array<{
    id: string;
    imageUrl: string;
    isMain: boolean;
    sortOrder: number;
  }>;
  category: {
    id: string;
    name: string;
  };
  seller: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export interface RelatedProductsResponse {
  relatedProducts: RelatedProduct[];
  total: number;
  relationshipType: 'category' | 'seller' | 'similar';
  categoryName?: string;
}

// Query Keys
export const relatedProductsQueryKeys = {
  all: ['relatedProducts'] as const,
  byProduct: (productId: string) => [...relatedProductsQueryKeys.all, 'product', productId] as const,
  byCategory: (categoryId: string, excludeProductId?: string) => 
    [...relatedProductsQueryKeys.all, 'category', categoryId, { exclude: excludeProductId }] as const,
  bySeller: (sellerId: string, excludeProductId?: string) => 
    [...relatedProductsQueryKeys.all, 'seller', sellerId, { exclude: excludeProductId }] as const,
};

// Get related products by category
export const useRelatedProductsByCategoryQuery = (
  categoryId: string, 
  excludeProductId?: string, 
  limit: number = 6
) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: relatedProductsQueryKeys.byCategory(categoryId, excludeProductId),
    queryFn: async (): Promise<RelatedProductsResponse> => {
      const params = new URLSearchParams({
        limit: limit.toString(),
        status: 'active'
      });
      
      if (excludeProductId) {
        params.append('exclude', excludeProductId);
      }

      const response = await apiClient.get(`/products/category/${categoryId}/related?${params.toString()}`);
      return response.data;
    },
    enabled: !!categoryId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get related products by seller
export const useRelatedProductsBySellerQuery = (
  sellerId: string, 
  excludeProductId?: string, 
  limit: number = 6
) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: relatedProductsQueryKeys.bySeller(sellerId, excludeProductId),
    queryFn: async (): Promise<RelatedProductsResponse> => {
      const params = new URLSearchParams({
        limit: limit.toString(),
        status: 'active'
      });
      
      if (excludeProductId) {
        params.append('exclude', excludeProductId);
      }

      const response = await apiClient.get(`/products/seller/${sellerId}/related?${params.toString()}`);
      return response.data;
    },
    enabled: !!sellerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get smart related products (combines category, seller, and similar items)
export const useSmartRelatedProductsQuery = (productId: string, limit: number = 6) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: relatedProductsQueryKeys.byProduct(productId),
    queryFn: async (): Promise<{
      categoryRelated: RelatedProduct[];
      sellerRelated: RelatedProduct[];
      similarItems: RelatedProduct[];
      recommended: RelatedProduct[];
    }> => {
      const response = await apiClient.get(`/products/${productId}/related?limit=${limit}`);
      return response.data;
    },
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for getting the best related products mix
export const useBestRelatedProducts = (
  productId: string,
  categoryId: string,
  sellerId: string,
  limit: number = 6
) => {
  const categoryQuery = useRelatedProductsByCategoryQuery(categoryId, productId, Math.ceil(limit * 0.6));
  const sellerQuery = useRelatedProductsBySellerQuery(sellerId, productId, Math.ceil(limit * 0.4));

  const combinedProducts = React.useMemo(() => {
    const categoryProducts = categoryQuery.data?.relatedProducts || [];
    const sellerProducts = sellerQuery.data?.relatedProducts || [];
    
    // Combine and deduplicate products
    const allProducts = [...categoryProducts];
    
    // Add seller products that aren't already in category products
    sellerProducts.forEach(sellerProduct => {
      if (!allProducts.find(p => p.id === sellerProduct.id)) {
        allProducts.push(sellerProduct);
      }
    });

    // Limit to requested number
    return allProducts.slice(0, limit);
  }, [categoryQuery.data, sellerQuery.data, limit]);

  return {
    products: combinedProducts,
    isLoading: categoryQuery.isLoading || sellerQuery.isLoading,
    error: categoryQuery.error || sellerQuery.error,
    categoryData: categoryQuery.data,
    sellerData: sellerQuery.data,
    refetch: () => {
      categoryQuery.refetch();
      sellerQuery.refetch();
    }
  };
};
