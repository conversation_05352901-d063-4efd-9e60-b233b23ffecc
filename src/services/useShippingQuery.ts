import { useQuery, useMutation } from '@tanstack/react-query';
import { useAuthenticatedApi } from './useAuthQuery';

// Query Keys
export const shippingQueryKeys = {
  all: ['shipping'] as const,
  rates: (data: any) => [...shippingQueryKeys.all, 'rates', data] as const,
  options: (addressId: string, cartItems: any[]) => [...shippingQueryKeys.all, 'options', addressId, cartItems] as const,
  zones: () => [...shippingQueryKeys.all, 'zones'] as const,
  estimate: (productId: string, destination: any) => [...shippingQueryKeys.all, 'estimate', productId, destination] as const,
  tracking: (trackingNumber: string) => [...shippingQueryKeys.all, 'tracking', trackingNumber] as const,
};

// Types
export interface ShippingRate {
  carrier: string;
  service: string;
  cost: number;
  currency: string;
  estimatedDays: number;
  trackingAvailable: boolean;
}

export interface ShippingCalculationData {
  fromCountry: string;
  fromCity: string;
  fromPostalCode?: string;
  toCountry: string;
  toCity: string;
  toPostalCode?: string;
  weight: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  shippingMethod?: 'standard' | 'express' | 'overnight';
}

export interface ShippingZone {
  id: string;
  name: string;
  countries: string[];
  baseRate: number;
  description: string;
}

export interface TrackingInfo {
  trackingNumber: string;
  status: string;
  location: string;
  estimatedDelivery?: string;
  trackingHistory: any[];
}

// Calculate Shipping Rates
export const useCalculateShippingRates = () => {
  const apiClient = useAuthenticatedApi();

  return useMutation({
    mutationFn: async (data: ShippingCalculationData): Promise<{ rates: ShippingRate[]; fromAddress: string; toAddress: string; weight: number }> => {
      const response = await apiClient.post('/shipping/calculate', data);
      return response.data;
    },
  });
};

// Get Shipping Options for Checkout (with address object)
export const useShippingOptionsQuery = (shippingAddress: any, cartItems: any[], enabled: boolean = true) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: [...shippingQueryKeys.all, 'options-address', shippingAddress, cartItems],
    queryFn: async (): Promise<{ rates: ShippingRate[]; fromAddress: string; toAddress: string; weight: number }> => {
      const response = await apiClient.post('/shipping/options', {
        shippingAddress,
        cartItems
      });
      return response.data;
    },
    enabled: enabled && !!shippingAddress?.country && cartItems.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Shipping Options for Checkout (legacy with address ID)
export const useShippingOptionsQueryById = (shippingAddressId: string, cartItems: any[], enabled: boolean = true) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: shippingQueryKeys.options(shippingAddressId, cartItems),
    queryFn: async (): Promise<{ rates: ShippingRate[]; fromAddress: string; toAddress: string; weight: number }> => {
      const response = await apiClient.post('/shipping/options', {
        shippingAddressId,
        cartItems
      });
      return response.data;
    },
    enabled: enabled && !!shippingAddressId && cartItems.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Shipping Zones
export const useShippingZonesQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: shippingQueryKeys.zones(),
    queryFn: async (): Promise<{ zones: ShippingZone[] }> => {
      const response = await apiClient.get('/shipping/zones');
      return response.data;
    },
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Estimate Shipping Cost for Product
export const useEstimateShippingCostQuery = (
  productId: string, 
  destination: { country: string; city?: string; postalCode?: string }, 
  quantity: number = 1,
  enabled: boolean = true
) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: shippingQueryKeys.estimate(productId, destination),
    queryFn: async (): Promise<{ rates: ShippingRate[]; productId: string; quantity: number }> => {
      const params = new URLSearchParams({
        productId,
        toCountry: destination.country,
        quantity: quantity.toString(),
        ...(destination.city && { toCity: destination.city }),
        ...(destination.postalCode && { toPostalCode: destination.postalCode }),
      });

      const response = await apiClient.get(`/shipping/estimate?${params}`);
      return response.data;
    },
    enabled: enabled && !!productId && !!destination.country,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Track Shipment
export const useTrackShipmentQuery = (trackingNumber: string, enabled: boolean = true) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: shippingQueryKeys.tracking(trackingNumber),
    queryFn: async (): Promise<TrackingInfo> => {
      const response = await apiClient.get(`/shipping/track/${trackingNumber}`);
      return response.data;
    },
    enabled: enabled && !!trackingNumber,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes for active tracking
  });
};

// Utility hook to get cheapest shipping option
export const useCheapestShippingOption = (rates: ShippingRate[] | undefined) => {
  if (!rates || rates.length === 0) return null;
  
  return rates.reduce((cheapest, current) => 
    current.cost < cheapest.cost ? current : cheapest
  );
};

// Utility hook to get fastest shipping option
export const useFastestShippingOption = (rates: ShippingRate[] | undefined) => {
  if (!rates || rates.length === 0) return null;
  
  return rates.reduce((fastest, current) => 
    current.estimatedDays < fastest.estimatedDays ? current : fastest
  );
};

// Utility hook to format shipping cost
export const useFormatShippingCost = () => {
  return (cost: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(cost);
  };
};
