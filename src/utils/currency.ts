import { SupportedCurrency } from '@/contexts/CurrencyLanguageContext'

// Fallback exchange rates (only for emergency use - use backend API instead)
const FALLBACK_RATES = {
  USD_TO_IDR: 15500, // Updated to match current backend rate
  IDR_TO_USD: 1 / 15500,
} as const

export interface CurrencyConversionOptions {
  fromCurrency: SupportedCurrency
  toCurrency: SupportedCurrency
  amount: number
}

/**
 * DEPRECATED: Use backend API for exchange rates instead
 * This is only for emergency fallback
 */
export function getFallbackExchangeRates(): { [key: string]: number } {
  console.warn('⚠️ Using fallback exchange rates - backend API should be used instead');

  return {
    'USD': 1,
    'IDR': FALLBACK_RATES.USD_TO_IDR,
  };
}

/**
 * DEPRECATED: Use useCurrencyConversion hook instead
 * Simple fallback conversion for emergency use only
 */
export function convertCurrencyFallback({ fromCurrency, toCurrency, amount }: CurrencyConversionOptions): number {
  console.warn('⚠️ Using fallback currency conversion - use backend API instead');

  if (fromCurrency === toCurrency) {
    return amount;
  }

  if (fromCurrency === 'USD' && toCurrency === 'IDR') {
    return amount * FALLBACK_RATES.USD_TO_IDR;
  }

  if (fromCurrency === 'IDR' && toCurrency === 'USD') {
    return amount * FALLBACK_RATES.IDR_TO_USD;
  }

  return amount;
}


/**
 * Format price with currency symbol
 */
export function formatPrice(amount: number, currency: SupportedCurrency): string {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency === 'IDR' ? 'IDR' : 'USD',
    minimumFractionDigits: currency === 'IDR' ? 0 : 2,
    maximumFractionDigits: currency === 'IDR' ? 0 : 2,
  });

  return formatter.format(amount);
}

/**
 * Get currency symbol
 */
export function getCurrencySymbol(currency: SupportedCurrency): string {
  return currency === 'USD' ? '$' : 'Rp';
}

/**
 * DEPRECATED: Use backend API for conversion
 * Simple conversion for backward compatibility only
 */
export function convertCurrency(amount: number, fromCurrency: SupportedCurrency, toCurrency: SupportedCurrency): number {
  console.warn('⚠️ Using deprecated convertCurrency - use backend API instead');

  return convertCurrencyFallback({ fromCurrency, toCurrency, amount });
}

/**
 * DEPRECATED: Use backend API for conversion
 * Format price with conversion
 */
export function convertAndFormatPrice(amount: number, fromCurrency: SupportedCurrency, toCurrency: SupportedCurrency): string {
  console.warn('⚠️ Using deprecated convertAndFormatPrice - use backend API instead');

  const convertedAmount = convertCurrencyFallback({ fromCurrency, toCurrency, amount });
  return formatPrice(convertedAmount, toCurrency);
}
