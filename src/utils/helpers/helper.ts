import { ProductItem } from "@/types/product";

interface BuildUrlState {
  baseUrl: string;
  query: Record<string, any>;
}

export const buildUrl = ({ baseUrl, query }: BuildUrlState): string => {
  const queryString = new URLSearchParams(query).toString();

  return `${baseUrl}?${queryString}`;
};

export const formatUSD = (value: number) =>
  new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);

export const formatCurrency = (
  value: number,
  currency: "USD" | "IDR" = "USD"
) =>
  new Intl.NumberFormat(currency === "USD" ? "en-US" : "id-ID", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: currency === "IDR" ? 0 : 2,
    maximumFractionDigits: currency === "IDR" ? 0 : 2,
  }).format(value);

export const parseUSD = (value: string) =>
  parseFloat(value.replace(/[^0-9.]/g, "")) || 0;

export const getTimeLeftString = (endDateStr: string): string => {
  const now: Date = new Date();
  const endDate: Date = new Date(endDateStr);
  const diffMs: number = endDate.getTime() - now.getTime();

  if (diffMs <= 0) return "Ended";

  const totalMinutes: number = Math.floor(diffMs / 60000);
  const days: number = Math.floor(totalMinutes / (24 * 60));
  const hours: number = Math.floor((totalMinutes % (24 * 60)) / 60);
  const minutes: number = totalMinutes % 60;

  return `${days}D ${hours}H ${minutes}M`;
};

export const convertToProductItem = (
  product: any,
  formatPrice: (amount: number) => string,
  convertPrice: (amount: number, fromCurrency: "USD" | "IDR") => number
): ProductItem => {
  const mainImage =
    product.images?.find((img: any) => img.isMain) || product.images?.[0];

  const basePrice = Number(product.currentBid ?? product.priceUSD);
  const convertedPrice = convertPrice(basePrice, "USD");
  const formattedPrice = formatPrice(convertedPrice);

  return {
    id: product.id,
    image: mainImage?.imageUrl || "/assets/images/placeholder.png",
    images: product.images?.map((img: any) => img.imageUrl) || [],
    title: product.itemName,
    price: formattedPrice,
    bids: product.bidCount?.toString() || "0",
    slug: product.slug,
    type: product.sellType,
    timeLeft: product.auctionEndDate
      ? new Date(product.auctionEndDate) > new Date()
        ? `${getTimeLeftString(product.auctionEndDate)}`
        : "Ended"
      : undefined,
  };
};
