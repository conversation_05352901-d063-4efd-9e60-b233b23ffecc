import axios, { AxiosError } from 'axios';

// Types
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface AuthResponse {
  status: boolean;
  message: string;
  data: {
    accessToken: string;
    refreshToken: string;
    user: User;
    expiresAt: number;
  };
}

export interface ProfileResponse {
  status: boolean;
  message: string;
  data: User & {
    createdAt: string;
    updatedAt: string;
  };
}

// Constants
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1';
const TOKEN_STORAGE_KEY = 'auth_tokens';
const REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiry

// Token management
export class TokenManager {
  private static instance: TokenManager;
  private refreshPromise: Promise<AuthTokens> | null = null;

  static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  // Store tokens securely
  setTokens(tokens: AuthTokens): void {
    if (typeof window !== 'undefined') {
      // Store in httpOnly cookie would be more secure, but for now using localStorage
      localStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(tokens));
    }
  }

  // Get stored tokens
  getTokens(): AuthTokens | null {
    if (typeof window === 'undefined') return null;
    
    try {
      const stored = localStorage.getItem(TOKEN_STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  }

  // Clear tokens
  clearTokens(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(TOKEN_STORAGE_KEY);
    }
  }

  // Check if token needs refresh
  needsRefresh(): boolean {
    const tokens = this.getTokens();
    if (!tokens) return false;
    
    const now = Date.now();
    const expiryTime = tokens.expiresAt * 1000;
    return (expiryTime - now) <= REFRESH_THRESHOLD;
  }

  // Check if token is expired
  isExpired(): boolean {
    const tokens = this.getTokens();
    if (!tokens) return true;
    
    const now = Date.now();
    const expiryTime = tokens.expiresAt * 1000;
    return now >= expiryTime;
  }

  // Refresh tokens
  async refreshTokens(): Promise<AuthTokens> {
    // Prevent multiple simultaneous refresh requests
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    const tokens = this.getTokens();
    if (!tokens?.refreshToken) {
      throw new Error('No refresh token available');
    }

    this.refreshPromise = this.performRefresh(tokens.refreshToken);
    
    try {
      const newTokens = await this.refreshPromise;
      this.setTokens(newTokens);
      return newTokens;
    } finally {
      this.refreshPromise = null;
    }
  }

  private async performRefresh(refreshToken: string): Promise<AuthTokens> {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/refresh-token`, {
        refreshToken
      });

      if (!response.data.status) {
        throw new Error(response.data.message || 'Token refresh failed');
      }

      return {
        accessToken: response.data.data.accessToken,
        refreshToken: response.data.data.refreshToken,
        expiresAt: response.data.data.expiresAt,
      };
    } catch (error) {
      this.clearTokens();
      throw error;
    }
  }
}

// HTTP client with automatic token management
export class AuthenticatedHttpClient {
  private tokenManager: TokenManager;

  constructor() {
    this.tokenManager = TokenManager.getInstance();
    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor to add auth header
    axios.interceptors.request.use(
      async (config) => {
        // Skip auth for auth endpoints
        if (config.url?.includes('/auth/login') || 
            config.url?.includes('/auth/register') || 
            config.url?.includes('/auth/refresh-token')) {
          return config;
        }

        const tokens = this.tokenManager.getTokens();
        if (!tokens) {
          throw new Error('No authentication tokens available');
        }

        // Refresh token if needed
        if (this.tokenManager.needsRefresh()) {
          try {
            await this.tokenManager.refreshTokens();
            const newTokens = this.tokenManager.getTokens();
            if (newTokens) {
              config.headers.Authorization = `Bearer ${newTokens.accessToken}`;
            }
          } catch (error) {
            console.error('Token refresh failed:', error);
            throw error;
          }
        } else {
          config.headers.Authorization = `Bearer ${tokens.accessToken}`;
        }

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle auth errors
    axios.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        const originalRequest = error.config as any;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            await this.tokenManager.refreshTokens();
            const tokens = this.tokenManager.getTokens();
            
            if (tokens && originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${tokens.accessToken}`;
              return axios(originalRequest);
            }
          } catch (refreshError) {
            this.tokenManager.clearTokens();
            // Redirect to login page
            if (typeof window !== 'undefined') {
              window.location.href = '/auth/login';
            }
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // Get authenticated axios instance
  getClient() {
    return axios;
  }
}

// Auth service functions
export const authService = {
  // Login with credentials
  async login(emailPhoneNumber: string, password: string): Promise<AuthResponse> {
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      emailPhoneNumber,
      password
    });

    if (response.data.status) {
      const tokenManager = TokenManager.getInstance();
      tokenManager.setTokens({
        accessToken: response.data.data.accessToken,
        refreshToken: response.data.data.refreshToken,
        expiresAt: response.data.data.expiresAt,
      });
    }

    return response.data;
  },

  // Login with Google
  async googleLogin(googleToken: string): Promise<AuthResponse> {
    const response = await axios.post(`${API_BASE_URL}/auth/google`, {
      googleToken
    });

    if (response.data.status) {
      const tokenManager = TokenManager.getInstance();
      tokenManager.setTokens({
        accessToken: response.data.data.accessToken,
        refreshToken: response.data.data.refreshToken,
        expiresAt: response.data.data.expiresAt,
      });
    }

    return response.data;
  },

  // Get user profile
  async getProfile(): Promise<ProfileResponse> {
    const client = new AuthenticatedHttpClient();
    const response = await client.getClient().get(`${API_BASE_URL}/auth/profile`);
    return response.data;
  },

  // Logout
  async logout(): Promise<void> {
    const tokenManager = TokenManager.getInstance();
    tokenManager.clearTokens();
    
    // Optional: Call logout endpoint on server
    // await axios.post(`${API_BASE_URL}/auth/logout`);
  },

  // Check if user is authenticated
  isAuthenticated(): boolean {
    const tokenManager = TokenManager.getInstance();
    return !tokenManager.isExpired();
  },

  // Get current user from tokens
  getCurrentUser(): User | null {
    const tokenManager = TokenManager.getInstance();
    const tokens = tokenManager.getTokens();
    
    if (!tokens) return null;

    try {
      // Decode JWT payload (basic decode, not verification)
      const payload = JSON.parse(atob(tokens.accessToken.split('.')[1]));
      return {
        id: payload.id,
        firstName: payload.firstName,
        lastName: payload.lastName,
        email: payload.email,
        phoneNumber: payload.phoneNumber,
      };
    } catch {
      return null;
    }
  }
};
