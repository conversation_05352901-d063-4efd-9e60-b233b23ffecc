'use client'
import {
    Box,
    Container,
    Flex,
    Heading,
    HTMLChakraProps,
    Text
} from '@chakra-ui/react'
import ProductCard from '../product/ProductCard'
import { ProductItem } from '@/types/product'
import Link from 'next/link'
import { useTranslations } from 'next-intl'
import ScrollableContainer from '@/components/ui/ScrollableContainer'

interface ProductCategoryProps {
    items: ProductItem[]
    title?: string
    maxWidth?: HTMLChakraProps<'div'>['maxW']
    showItemCount?: boolean
    showViewAll?: boolean
    viewAllHref?: string
    scrollAmount?: number
    dragMultiplier?: number
    containerProps?: React.ComponentProps<typeof Box>
    headerProps?: React.ComponentProps<typeof Flex>
}

const ProductCategory: React.FC<ProductCategoryProps> = ({
    items,
    title,
    maxWidth = { lg: '100%' },
    showItemCount = true,
    showViewAll = true,
    viewAllHref = '/auction',
    scrollAmount = 300,
    dragMultiplier = 2,
    containerProps = {},
    headerProps = {},
}) => {
    const t = useTranslations()

    return (
        <Box my={2} bg="white" {...containerProps}>
            <Container maxW={maxWidth}>
                <Flex
                    justifyContent="space-between"
                    alignItems="center"
                    gap={4}
                    {...headerProps}
                >
                    {title && (
                        <Heading
                            size={{ base: 'md', md: 'lg', lg: '2xl' }}
                            py={6}
                            color="gray.800"
                        >
                            {title}
                        </Heading>
                    )}

                    {(showItemCount || showViewAll) && (
                        <Flex
                            flexDirection={{ base: 'column', sm: 'row' }}
                            alignItems="center"
                        >
                            {showItemCount && (
                                <Text
                                    whiteSpace="nowrap"
                                    fontSize={{ base: "xs", sm: "md" }}
                                    color="gray.600"
                                >
                                    {t('showing')}{" "}
                                    <Box as="span" fontWeight="bold" color="gray.800">
                                        1 - {items.length}
                                    </Box>{" "}
                                    {t('of')}{" "}
                                    <Box as="span" fontWeight="bold" color="gray.800">
                                        {items.length}
                                    </Box>
                                </Text>
                            )}

                            {showViewAll && (
                                <Link href={viewAllHref}>
                                    <Text
                                        whiteSpace="nowrap"
                                        fontSize={{ base: "sm", sm: "md" }}
                                        color="gray.600"
                                        ml={showItemCount ? 4 : 0}
                                        textDecoration="underline"
                                        fontWeight="semibold"
                                        _hover={{ color: 'gray.800' }}
                                    >
                                        {t('viewAll')}
                                    </Text>
                                </Link>
                            )}
                        </Flex>
                    )}
                </Flex>

                <ScrollableContainer
                    pb={8}
                    scrollAmount={scrollAmount}
                    dragMultiplier={dragMultiplier}
                >
                    {items.map((item) => (
                        <ProductCard
                            imageContainerProps={{
                                h: { base: '200px', md: '300px' },
                                w: { base: '170px', md: '250px' },
                            }}
                            imageProps={{
                                maxW: { base: '140px', md: '220px' },
                                maxH: { base: '160px', md: '240px' },
                            }}
                            key={item.id}
                            item={item}
                        />
                    ))}
                </ScrollableContainer>
            </Container>
        </Box>
    )
}

export default ProductCategory
