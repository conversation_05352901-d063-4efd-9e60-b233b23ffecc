import React from 'react'
import { Box, Button, HTMLChakraProps, Icon, Image, Text } from '@chakra-ui/react'
import { FaRegHeart } from 'react-icons/fa'
import { ProductItem } from '@/types/product'

interface ProductCardImageProps {
  item: ProductItem
  backgroundColorCardImage?: string
  containerProps?: React.ComponentProps<typeof Box>
  imageProps?: React.ComponentProps<typeof Image>
  boxSizeWatchList?: number
}

const ProductCardImage: React.FC<ProductCardImageProps> = ({
  item,
  backgroundColorCardImage = 'gray.100',
  containerProps = {},
  imageProps = {},
  boxSizeWatchList = 4.5,
}) => {
  return (
    <Box
      position="relative"
      bg={backgroundColorCardImage}
      display="flex"
      justifyContent="center"
      alignItems="center"
      p={{ base: 4, md: 6 }}
      borderRadius="sm"
      aspectRatio="1/1"
      overflow="hidden"
      {...containerProps}
    >
      <Box
        position="absolute"
        top={3}
        right={3}
        zIndex={1}
      >
        <Button
          variant="ghost"
          color="gray.600"
          p={1}
          minW="auto"
          h="auto"
          display="flex"
          flexDirection="column"
          gap={0}
        >
          <Icon
            as={FaRegHeart}
            boxSize={boxSizeWatchList}
            color="gray.600"
            _hover={{ color: 'red.500' }}
            transition="color 0.2s"
          />
          <Text fontSize="xs">24</Text>
        </Button>
      </Box>

      <Image
        src={item.image}
        alt={item.title}
        draggable="false"
        objectFit="contain"
        maxW="90%"
        maxH="90%"
        w="auto"
        h="auto"
        {...imageProps}
      />
    </Box>
  )
}

export default ProductCardImage