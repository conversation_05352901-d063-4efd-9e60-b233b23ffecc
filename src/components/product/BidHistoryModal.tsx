'use client'
import React, { memo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,

  Spinner,
  Grid,
  GridItem,
  Card,
  Separator,
  Button
} from '@chakra-ui/react';
// Using Portal and Box as alternative to Modal for Chakra UI v3.2
import { Portal } from '@chakra-ui/react';

import { useBidHistoryQuery, useBidActivityQuery } from '@/services/useBidHistoryQuery';
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext';
import { formatDistanceToNow } from 'date-fns';
import { FaTrophy, FaClock, FaUsers, FaChartLine } from 'react-icons/fa';

interface BidHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  productName: string;
}

const BidHistoryModal: React.FC<BidHistoryModalProps> = memo(({
  isOpen,
  onClose,
  productId,
  productName
}) => {
  const { formatPrice, convertPrice, currency } = useCurrencyLanguage();
  const { data: bidHistoryData, isLoading: isLoadingHistory } = useBidHistoryQuery(productId, 1, 20);
  const { data: activityData, isLoading: isLoadingActivity } = useBidActivityQuery(productId);

  const isLoading = isLoadingHistory || isLoadingActivity;

  if (!isOpen) return null;

  return (
    <Portal>
      {/* Backdrop */}
      <Box
        position="fixed"
        top="0"
        left="0"
        w="100vw"
        h="100vh"
        bg="blackAlpha.600"
        zIndex="modal"
        onClick={onClose}
      />

      {/* Modal Content */}
      <Box
        position="fixed"
        top="50%"
        left="50%"
        transform="translate(-50%, -50%)"
        w="90vw"
        maxW="4xl"
        maxH="90vh"
        bg="white"
        borderRadius="lg"
        boxShadow="xl"
        zIndex="modal"
        overflow="hidden"
      >
        {/* Header */}
        <HStack justify="space-between" align="center" p={6} borderBottom="1px" borderColor="gray.200">
          <Text fontSize="xl" fontWeight="bold">
            Bid History - {productName}
          </Text>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            borderRadius="full"
          >
            ✕
          </Button>
        </HStack>

        {/* Body */}
        <Box p={6} overflowY="auto" maxH="calc(90vh - 120px)">
          {isLoading ? (
            <Box textAlign="center" py={8}>
              <Spinner size="lg" />
              <Text mt={4} color="gray.600">Loading bid history...</Text>
            </Box>
          ) : (
            <VStack gap={6} align="stretch">
              {/* Bid Statistics */}
              {bidHistoryData?.stats && (
                <Grid templateColumns={{ base: '1fr', md: 'repeat(4, 1fr)' }} gap={4}>
                  <GridItem>
                    <Card.Root>
                      <Card.Body textAlign="center" py={4}>
                        <HStack justify="center" mb={2}>
                          <FaChartLine color="blue" />
                          <Text fontSize="sm" fontWeight="medium" color="gray.600">
                            Total Bids
                          </Text>
                        </HStack>
                        <Text fontSize="2xl" fontWeight="bold">
                          {bidHistoryData.stats.totalBids}
                        </Text>
                      </Card.Body>
                    </Card.Root>
                  </GridItem>

                  <GridItem>
                    <Card.Root>
                      <Card.Body textAlign="center" py={4}>
                        <HStack justify="center" mb={2}>
                          <FaUsers color="green" />
                          <Text fontSize="sm" fontWeight="medium" color="gray.600">
                            Bidders
                          </Text>
                        </HStack>
                        <Text fontSize="2xl" fontWeight="bold">
                          {bidHistoryData.stats.uniqueBidders}
                        </Text>
                      </Card.Body>
                    </Card.Root>
                  </GridItem>

                  <GridItem>
                    <Card.Root>
                      <Card.Body textAlign="center" py={4}>
                        <HStack justify="center" mb={2}>
                          <FaTrophy color="gold" />
                          <Text fontSize="sm" fontWeight="medium" color="gray.600">
                            Highest Bid
                          </Text>
                        </HStack>
                        <Text fontSize="lg" fontWeight="bold" color="green.600">
                          {formatPrice(convertPrice(bidHistoryData.stats.currentHighestBid, 'USD'), currency)}
                        </Text>
                      </Card.Body>
                    </Card.Root>
                  </GridItem>

                  <GridItem>
                    <Card.Root>
                      <Card.Body textAlign="center" py={4}>
                        <HStack justify="center" mb={2}>
                          <FaChartLine color="purple" />
                          <Text fontSize="sm" fontWeight="medium" color="gray.600">
                            Average Bid
                          </Text>
                        </HStack>
                        <Text fontSize="lg" fontWeight="bold">
                          {formatPrice(convertPrice(bidHistoryData.stats.averageBid, 'USD'), currency)}
                        </Text>
                      </Card.Body>
                    </Card.Root>
                  </GridItem>
                </Grid>
              )}

              {/* Recent Activity */}
              {activityData?.recentActivity && activityData.recentActivity.length > 0 && (
                <Box>
                  <HStack mb={4}>
                    <FaClock />
                    <Text fontSize="lg" fontWeight="semibold">Recent Activity (24h)</Text>
                  </HStack>
                  <VStack gap={2} align="stretch">
                    {activityData.recentActivity.slice(0, 5).map((activity) => (
                      <Card.Root key={activity.id} size="sm">
                        <Card.Body py={3}>
                          <HStack justify="space-between">
                            <HStack>
                              <Text fontSize="sm" fontWeight="medium">
                                {activity.bidderName}
                              </Text>
                              <Text fontSize="sm" color="gray.600">
                                placed a bid
                              </Text>
                            </HStack>
                            <HStack>
                              <Text fontSize="sm" fontWeight="bold" color="green.600">
                                {formatPrice(convertPrice(activity.amount, 'USD'), currency)}
                              </Text>
                              <Text fontSize="xs" color="gray.500">
                                {activity.timeAgo}
                              </Text>
                            </HStack>
                          </HStack>
                        </Card.Body>
                      </Card.Root>
                    ))}
                  </VStack>
                </Box>
              )}

              <Separator />

              {/* Bid History List */}
              <Box>
                <Text fontSize="lg" fontWeight="semibold" mb={4}>
                  All Bids ({bidHistoryData?.stats.totalBids || 0})
                </Text>
                
                {bidHistoryData?.bidHistory && bidHistoryData.bidHistory.length > 0 ? (
                  <VStack gap={3} align="stretch" maxH="400px" overflowY="auto">
                    {bidHistoryData.bidHistory.map((bid, index) => (
                      <Card.Root key={bid.id} variant={bid.isWinning ? "elevated" : "outline"}>
                        <Card.Body py={4}>
                          <HStack justify="space-between" align="start">
                            <HStack gap={3}>
                              <Box
                                w="40px"
                                h="40px"
                                borderRadius="full"
                                bg="gray.200"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                                fontSize="sm"
                                fontWeight="bold"
                                color="gray.600"
                              >
                                {bid.bidder.name.charAt(0).toUpperCase()}
                              </Box>
                              <VStack align="start" gap={1}>
                                <HStack>
                                  <Text fontWeight="medium">
                                    {bid.bidder.name}
                                  </Text>
                                  {bid.isWinning && (
                                    <Badge colorScheme="green" size="sm">
                                      <FaTrophy style={{ marginRight: '4px' }} />
                                      Winning
                                    </Badge>
                                  )}
                                  {index === 0 && !bid.isWinning && (
                                    <Badge colorScheme="blue" size="sm">
                                      Latest
                                    </Badge>
                                  )}
                                </HStack>
                                <Text fontSize="xs" color="gray.600">
                                  {bid.bidder.displayEmail}
                                </Text>
                              </VStack>
                            </HStack>

                            <VStack align="end" gap={1}>
                              <Text 
                                fontSize="lg" 
                                fontWeight="bold" 
                                color={bid.isWinning ? "green.600" : "gray.800"}
                              >
                                {formatPrice(convertPrice(bid.amount, 'USD'), currency)}
                              </Text>
                              <Text fontSize="xs" color="gray.500">
                                {formatDistanceToNow(new Date(bid.bidTime), { addSuffix: true })}
                              </Text>
                              <Badge size="xs" variant="outline">
                                Bid #{bid.bidNumber}
                              </Badge>
                            </VStack>
                          </HStack>
                        </Card.Body>
                      </Card.Root>
                    ))}
                  </VStack>
                ) : (
                  <Box textAlign="center" py={8}>
                    <Text color="gray.600">No bids yet for this auction.</Text>
                  </Box>
                )}
              </Box>

              {/* Pagination info */}
              {bidHistoryData?.pagination && bidHistoryData.pagination.totalPages > 1 && (
                <Box textAlign="center">
                  <Text fontSize="sm" color="gray.600">
                    Showing page {bidHistoryData.pagination.page} of {bidHistoryData.pagination.totalPages}
                  </Text>
                </Box>
              )}
            </VStack>
          )}
        </Box>
      </Box>
    </Portal>
  );
});

BidHistoryModal.displayName = 'BidHistoryModal';

export default BidHistoryModal;
