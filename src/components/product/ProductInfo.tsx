'use client'

import { Box, Heading, Text, BoxProps } from '@chakra-ui/react'

interface ProductInfoProps extends BoxProps {
  title: string
  subtitle?: string
  description?: string
  titleSize?: string
  subtitleColor?: string
  descriptionSize?: string
}

const ProductInfo: React.FC<ProductInfoProps> = ({
  title,
  subtitle,
  description,
  titleSize = "xl",
  subtitleColor = "gray.500",
  descriptionSize = "sm",
  ...boxProps
}) => {
  return (
    <Box {...boxProps}>
      <Text fontSize={titleSize} fontWeight="bold" mb={1}>
        {title}
      </Text>
      
      {subtitle && (
        <Text fontSize="md" color={subtitleColor} fontWeight="semibold" mb={4}>
          {subtitle}
        </Text>
      )}
    </Box>
  )
}

export default ProductInfo
