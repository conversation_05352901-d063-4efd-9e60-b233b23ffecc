'use client'

import { Box, Flex, BoxProps } from '@chakra-ui/react'

interface ProductDetailLayoutProps extends BoxProps {
  leftContent: React.ReactNode
  rightContent: React.ReactNode
  leftProps?: BoxProps
  rightProps?: BoxProps
  containerProps?: BoxProps
}

const ProductDetailLayout: React.FC<ProductDetailLayoutProps> = ({
  leftContent,
  rightContent,
  leftProps = {},
  rightProps = {},
  containerProps = {},
  ...boxProps
}) => {
  return (
    <Box {...boxProps}>
      <Box mx="auto" {...containerProps}>
        <Flex
          align="flex-start"
          mx="auto"
          direction={{ base: 'column', lg: 'row' }}
          w="100%"
        >
          <Box
            flex={{ base: 'none', lg: '1' }}
            alignSelf="flex-start"
            w={{ base: '100%', lg: '50%' }}
            maxW={{ lg: '700px' }}
            {...leftProps}
          >
            {leftContent}
          </Box>

          <Box
            flex={{ base: 'none', lg: '1' }}
            alignSelf="flex-start"
            bg="white"
            px={{ base: 4, md: 8, lg: 20 }}
            pt={{ base: 8, md: 12, lg: 12 }}
            pb={{ base: 8, md: 12 }}
            minH={{ lg: '600px' }}
            w="full"
            {...rightProps}
          >
            {rightContent}
          </Box>
        </Flex>
      </Box>
    </Box>
  )
}

export default ProductDetailLayout
