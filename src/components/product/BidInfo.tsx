'use client'

import { 
  Box, 
  <PERSON>lex, 
  <PERSON>ing, 
  Text, 
  Button, 
  VStack, 
  HStack, 
  Icon,
  BoxProps 
} from '@chakra-ui/react'
import { FaClock, FaHistory } from 'react-icons/fa'

interface BidInfoProps extends BoxProps {
  currentBid: string
  bidCount: number
  timeLeft: string
  endDate?: string
  onPlaceBid: () => void
  onShowBidHistory?: () => void
  buttonText?: string
  buttonSize?: "2xs" | "xs" | "sm" | "md" | "lg" | "xl" | "2xl"
  buttonRadius?: number
}

const BidInfo: React.FC<BidInfoProps> = ({
  currentBid,
  bidCount,
  timeLeft,
  endDate,
  onPlaceBid,
  onShowBidHistory,
  buttonText = "Place Bid",
  buttonSize = "lg",
  buttonRadius = 30,
  ...boxProps
}) => {
  return (
    <Box {...boxProps}>
      <HStack justifyContent="space-between" alignItems="start" gap={4} mb={4}>
        <Box>
          <Text fontSize="sm" fontWeight="semibold" color="gray.500" mb={1}>
            Current Bid:
          </Text>
          <Heading size="6xl" color="black" fontWeight="bold">
            {currentBid}
          </Heading>
        </Box>
        
        <Box>
          <Text fontSize="sm" fontWeight="semibold" color="gray.500" mb={1}>
            Bids
          </Text>
          <Text fontSize="4xl" fontWeight="bold">
            {bidCount}
          </Text>
          {onShowBidHistory && (
            <Text 
              cursor="pointer" 
              fontSize={12} 
              onClick={onShowBidHistory} 
              textDecoration="underline"
              _hover={{ color: 'gray.800' }}
            >
              <Icon as={FaHistory} boxSize={3} color="gray.600" me={2} />
              Show bid history
            </Text>
          )}
        </Box>
      </HStack>

      <Flex justify="space-between" mb={6}>
        <Box textAlign="left">
          <Text fontSize="sm" fontWeight="semibold" color="gray.500">
            Ends in
          </Text>
          <Flex align="start" justify="flex-end">
            <Icon as={FaClock} mr={1} />
            <Text color="gray.600" fontWeight="bold">
              {timeLeft}
              {endDate && ` | ${endDate}`}
            </Text>
          </Flex>
        </Box>
      </Flex>

      <VStack gap={4}>
        <Button
          size={buttonSize}
          w="full"
          borderRadius={buttonRadius}
          onClick={onPlaceBid}
        >
          {buttonText}
        </Button>
      </VStack>
    </Box>
  )
}

export default BidInfo
