"use client"
import React, { useState } from 'react'
import {
    Box,
    Button,
    Text,
    VStack,
    HStack,
    Badge,
    Input,
    Stack,
    Heading,
    Flex,
    IconButton,
    useDisclosure,
    Skeleton
} from '@chakra-ui/react'
import { FaMinus, FaPlus, FaShoppingCart, FaHeart } from 'react-icons/fa'
import { useRouter } from 'next/navigation'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import { useTranslations } from 'next-intl'

interface BuyNowInfoProps {
    price: number;
    productId: string;
    productName: string;
    onAddToCart?: (productId: string, quantity: number) => void;
    onBuyNow?: (productId: string, quantity: number) => void;
    onAddToWishlist?: (productId: string) => void;
    isInWishlist?: boolean;
    isInCart?: boolean;
    mb?: number;
    isLoadingAuth?: boolean;
    isAuthenticated?: boolean;
}

const BuyNowInfo: React.FC<BuyNowInfoProps> = ({
    price,
    productId,
    productName,
    onAddToCart,
    onBuyNow,
    onAddToWishlist,
    isInWishlist = false,
    isInCart = false,
    mb = 0,
    isLoadingAuth = false,
    isAuthenticated = false
}) => {
    const router = useRouter();
    const t = useTranslations();
    const { formatPrice, convertPrice, currency } = useCurrencyLanguage();
    const quantity = 1; // Fixed quantity for collectibles
    const [isAddingToCart, setIsAddingToCart] = useState(false);
    const [isBuying, setIsBuying] = useState(false);

    const handleAddToCart = async () => {
        if (onAddToCart) {
            setIsAddingToCart(true);
            try {
                await onAddToCart(productId, quantity);
            } finally {
                setIsAddingToCart(false);
            }
        }
    };

    const handleBuyNow = async () => {
        if (onBuyNow) {
            setIsBuying(true);
            try {
                await onBuyNow(productId, quantity);
            } finally {
                setIsBuying(false);
            }
        }
    };

    const handleAddToWishlist = () => {
        if (onAddToWishlist) {
            onAddToWishlist(productId);
        }
    };

    return (
        <Box
            bg="white"
            p={6}
            borderRadius="lg"
            border="1px solid"
            borderColor="gray.200"
            mb={mb}
        >
            <VStack align="stretch" gap={4}>
                {/* Price Section */}
                <Box textAlign="center" py={4}>
                    <Text fontSize="sm" color="gray.600" mb={3} textTransform="uppercase" letterSpacing="wide">
                        {t('Cart.price')}
                    </Text>
                    <Text fontSize="5xl" fontWeight="extrabold" color="gray.800" lineHeight="1">
                        {formatPrice(convertPrice(price, 'USD'), currency)}
                    </Text>
                    <Text fontSize="sm" color="gray.500" mt={2}>
                        Fixed price • No bidding required
                    </Text>
                </Box>

                {/* <HStack justify="center">
                    <Badge variant="plain" bg="green.100" color="gray.800" px={4} py={2} borderRadius="full">
                        ✓ Available for Purchase
                    </Badge>
                </HStack> */}

                {
                    isLoadingAuth ? (
                        <Skeleton
                            width="full"
                            height="30px"
                            borderRadius="lg" />

                    ) :
                        isAuthenticated ? (
                            <Stack gap={3}>
                                {/* Buy Now Button */}
                                <Button
                                    colorScheme="blue"
                                    size="lg"
                                    borderRadius="full"
                                    onClick={handleBuyNow}
                                    loading={isBuying}
                                    disabled={isBuying || isAddingToCart}
                                >
                                    {t('Product.buyNow')} - {formatPrice(convertPrice(price, 'USD'), currency)}
                                </Button>

                                <Button
                                    variant="outline"
                                    colorScheme="blue"
                                    borderRadius="full"
                                    size="lg"
                                    onClick={handleAddToCart}
                                    loading={isAddingToCart}
                                    disabled={isAddingToCart || isBuying || isInCart}
                                >
                                    <FaShoppingCart style={{ marginRight: '8px' }} />
                                    Add to Cart
                                </Button>

                                {/* Wishlist Button */}
                                {/* <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleAddToWishlist}
                        color={isInWishlist ? "red.500" : "gray.600"}
                        _hover={{
                            color: isInWishlist ? "red.600" : "red.500",
                            bg: isInWishlist ? "red.50" : "gray.50"
                        }}
                    >
                        <FaHeart style={{ marginRight: '8px' }} />
                        {isInWishlist ? 'Remove from Wishlist' : 'Add to Wishlist'}
                    </Button> */}
                            </Stack>
                        ) : (
                            <Button
                                size={'lg'}
                                colorScheme="blue"
                                w="full"
                                borderRadius={'full'}
                                onClick={() => router.push('/auth/login')}
                            >
                                Login to Buy / Bid
                            </Button>

                        )
                }



                {/* Product Info */}
                {/* <Box pt={4} borderTop="1px solid" borderColor="gray.100">
                    <Text fontSize="xs" color="gray.500">
                        • Free shipping on orders over $50
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                        • 30-day return policy
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                        • Secure payment processing
                    </Text>
                </Box> */}
            </VStack>
        </Box>
    );
};

export default BuyNowInfo;
