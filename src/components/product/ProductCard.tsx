'use client'

import { ProductItem } from '@/types/product'
import { Box, Flex, Heading, Image, Text } from '@chakra-ui/react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import ProductCardImage from './ProductCardImage'

interface ProductCardProps {
    item: ProductItem
    backgroundColor?: string
    backgroundColorCardImage?: string
    containerProps?: React.ComponentProps<typeof Box>
    imageContainerProps?: React.ComponentProps<typeof Box>
    imageProps?: React.ComponentProps<typeof Image>
    bodyContainerProps?: React.ComponentProps<typeof Box>
}

const ProductCard: React.FC<ProductCardProps> = ({
    item,
    backgroundColor = '',
    backgroundColorCardImage = 'gray.100',
    containerProps = {},
    imageContainerProps = {},
    imageProps = {},
    bodyContainerProps = {},
}) => {
    const t = useTranslations()
    return (
        <Box
            as="article"
            bg={backgroundColor}
            display="flex"
            flexDirection="column"
            w="100%"
            maxW={{ base: '170px', md: '270px' }}
            // maxH={{ base: 'auto', md: '400px' }}
            gap={3}
            {...containerProps}
        >
            <Link href={`/auction/${item.slug}`} draggable="false" tabIndex={-1}>
                <ProductCardImage
                    item={item}
                    backgroundColorCardImage={backgroundColorCardImage}
                    containerProps={imageContainerProps}
                    imageProps={imageProps}
                />
            </Link>

            <Box
                {...bodyContainerProps}
                px={2} w="full">
                <Link href={`/auction/${item.slug}`} draggable="false">
                    <Text
                        lineClamp={2}
                        fontSize="sm"
                        fontWeight="semibold"
                        color="gray.800"
                        _hover={{ textDecoration: 'underline' }}
                    >
                        {item.title}
                    </Text>
                </Link>

                <Flex mt={2} alignItems="center" flexWrap="wrap" gap={2}>
                    <Heading fontWeight="bold" fontSize={{ base: 'lg', md: 'xl' }} color="gray.800">
                        {item.price}
                    </Heading>
                    {
                        item.type === 'auction' && item.bids && (
                            <Text fontSize="sm" color="gray.500">
                                {item.bids ?? '0'} {t('Product.bidCount')}
                            </Text>
                        )
                    }
                </Flex>
                <Text mt={1} fontSize="sm" fontWeight={item.timeLeft === "Ended" ? "bold" : "normal"} color={item.timeLeft === 'Ended' ? 'red.600' : 'gray.600'}>
                    {item.timeLeft === 'Ended' ? t('Product.ended') : item.timeLeft ?? ''}
                </Text>
            </Box>
        </Box>
    )
}

export default ProductCard