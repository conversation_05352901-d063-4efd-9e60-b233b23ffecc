"use client"
import React from 'react'
import {
  <PERSON>,
  Stack,
  Text,
  Button,
  HStack,
  Input,
  Badge
} from '@chakra-ui/react'
import { useCategoriesQuery, useItemTypesQuery, ProductQueryParams } from '@/services/useProductQuery'

interface ProductFilterProps {
  filters: Partial<ProductQueryParams>;
  onFiltersChange: (filters: Partial<ProductQueryParams>) => void;
  onClearFilters: () => void;
}

const ProductFilter: React.FC<ProductFilterProps> = ({
  filters,
  onFiltersChange,
  onClearFilters
}) => {
  const { data: categories = [] } = useCategoriesQuery();
  const { data: itemTypes = [] } = useItemTypesQuery(filters.categoryId);

  const handleFilterChange = (key: keyof ProductQueryParams, value: any) => {
    const newFilters = { ...filters, [key]: value };
    
    // Reset item type when category changes
    if (key === 'categoryId') {
      delete newFilters.itemTypeId;
    }
    
    // Reset page when filters change
    delete newFilters.page;
    
    onFiltersChange(newFilters);
  };

  const activeFiltersCount = Object.values(filters).filter(value => 
    value !== undefined && value !== null && value !== ''
  ).length;

  return (
    <Box bg="white" p={6} borderRadius="lg" shadow="sm" borderWidth="1px">
      <HStack justify="space-between" mb={4}>
        <Text fontWeight="bold" fontSize="lg">
          Filters
        </Text>
        {activeFiltersCount > 0 && (
          <HStack>
            <Badge colorScheme="blue" variant="solid">
              {activeFiltersCount} active
            </Badge>
            <Button size="sm" variant="ghost" onClick={onClearFilters}>
              Clear All
            </Button>
          </HStack>
        )}
      </HStack>

      <Stack gap={4}>
        {/* Search */}
        <Box>
          <Text fontWeight="medium" mb={2} fontSize="sm">
            Search
          </Text>
          <Input
            placeholder="Search products..."
            value={filters.search || ''}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            size="sm"
          />
        </Box>

        {/* Sell Type */}
        <Box>
          <Text fontWeight="medium" mb={2} fontSize="sm">
            Type
          </Text>
          <select
            value={filters.sellType || ''}
            onChange={(e) => handleFilterChange('sellType', e.target.value || undefined)}
            style={{
              width: '100%',
              padding: '8px',
              borderRadius: '6px',
              border: '1px solid #e2e8f0',
              fontSize: '14px'
            }}
          >
            <option value="">All Types</option>
            <option value="auction">Auction</option>
            <option value="buy-now">Buy Now</option>
          </select>
        </Box>

        {/* Category */}
        <Box>
          <Text fontWeight="medium" mb={2} fontSize="sm">
            Category
          </Text>
          <select
            value={filters.categoryId || ''}
            onChange={(e) => handleFilterChange('categoryId', e.target.value || undefined)}
            style={{
              width: '100%',
              padding: '8px',
              borderRadius: '6px',
              border: '1px solid #e2e8f0',
              fontSize: '14px'
            }}
          >
            <option value="">All Categories</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </Box>

        {/* Item Type */}
        {filters.categoryId && (
          <Box>
            <Text fontWeight="medium" mb={2} fontSize="sm">
              Item Type
            </Text>
            <select
              value={filters.itemTypeId || ''}
              onChange={(e) => handleFilterChange('itemTypeId', e.target.value || undefined)}
              style={{
                width: '100%',
                padding: '8px',
                borderRadius: '6px',
                border: '1px solid #e2e8f0',
                fontSize: '14px'
              }}
            >
              <option value="">All Item Types</option>
              {itemTypes.map((itemType) => (
                <option key={itemType.id} value={itemType.id}>
                  {itemType.name}
                </option>
              ))}
            </select>
          </Box>
        )}

        {/* Status */}
        <Box>
          <Text fontWeight="medium" mb={2} fontSize="sm">
            Status
          </Text>
          <select
            value={filters.status || ''}
            onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
            style={{
              width: '100%',
              padding: '8px',
              borderRadius: '6px',
              border: '1px solid #e2e8f0',
              fontSize: '14px'
            }}
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="draft">Draft</option>
            <option value="sold">Sold</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </Box>

        {/* Sort By */}
        <Box>
          <Text fontWeight="medium" mb={2} fontSize="sm">
            Sort By
          </Text>
          <select
            value={`${filters.sortBy || 'createdAt'}-${filters.sortOrder || 'desc'}`}
            onChange={(e) => {
              const [sortBy, sortOrder] = e.target.value.split('-');
              handleFilterChange('sortBy', sortBy);
              handleFilterChange('sortOrder', sortOrder);
            }}
            style={{
              width: '100%',
              padding: '8px',
              borderRadius: '6px',
              border: '1px solid #e2e8f0',
              fontSize: '14px'
            }}
          >
            <option value="createdAt-desc">Newest First</option>
            <option value="createdAt-asc">Oldest First</option>
            <option value="priceUSD-desc">Price: High to Low</option>
            <option value="priceUSD-asc">Price: Low to High</option>
            <option value="auctionEndDate-asc">Ending Soon</option>
            <option value="bidCount-desc">Most Bids</option>
          </select>
        </Box>
      </Stack>
    </Box>
  );
};

export default ProductFilter;
