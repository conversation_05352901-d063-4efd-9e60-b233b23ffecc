'use client'

import { Box, Heading, Text, HStack, Icon, BoxProps } from '@chakra-ui/react'
import { FaArrowUpRightFromSquare } from 'react-icons/fa6'

interface SalesHistoryProps extends BoxProps {
  title?: string
  description?: string
  linkText?: string
  linkUrl?: string
  onLinkClick?: () => void
}

const SalesHistory: React.FC<SalesHistoryProps> = ({
  title = "Sales History:",
  description = "View recent sales history and more about this listing on Goldin trusted partner site : cardladder.com",
  linkText = "View Sales History on Card Ladder",
  linkUrl = "https://www.cardladder.com/",
  onLinkClick,
  ...boxProps
}) => {
  const handleClick = () => {
    if (onLinkClick) {
      onLinkClick()
    } else if (linkUrl) {
      window.open(linkUrl, '_blank', 'noopener noreferrer')
    }
  }

  return (
    <Box {...boxProps}>
      <Heading as="h3" size="md" mb={3}>
        {title}
      </Heading>
      
      <Text fontSize="sm" mb={4}>
        {description}
      </Text>
      
      <HStack
        justifyContent="space-between"
        cursor="pointer"
        bg="gray.100"
        py={3}
        px={4}
        borderRadius={8}
        onClick={handleClick}
        _hover={{
          bg: 'gray.200',
          transform: 'translateY(-1px)',
        }}
        transition="all 0.2s"
      >
        <Text fontSize="sm" fontWeight="normal" color="gray.800">
          {linkText}
        </Text>
        <Icon as={FaArrowUpRightFromSquare} boxSize={3.5} />
      </HStack>
    </Box>
  )
}

export default SalesHistory
