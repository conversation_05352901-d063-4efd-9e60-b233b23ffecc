'use client'

import React, { useCallback, useState } from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  HStack,
  Image,
  IconButton,
  Grid
} from '@chakra-ui/react';
import { useDropzone } from 'react-dropzone';
import { X, Upload, Image as ImageIcon } from 'lucide-react';
import { useUploadImagesMutation } from '@/services/useProductQuery';

interface ImageUploadProps {
  onImagesUploaded?: (urls: string[]) => void;
  maxFiles?: number;
  existingImages?: string[];
  onRemoveImage?: (index: number) => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImagesUploaded,
  maxFiles = 10,
  existingImages = [],
  onRemoveImage
}) => {
  const [previewImages, setPreviewImages] = useState<string[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  
  const uploadMutation = useUploadImagesMutation();

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.slice(0, maxFiles - existingImages.length - selectedFiles.length);
    
    // Create preview URLs
    const newPreviews = newFiles.map(file => URL.createObjectURL(file));
    
    setSelectedFiles(prev => [...prev, ...newFiles]);
    setPreviewImages(prev => [...prev, ...newPreviews]);
  }, [maxFiles, existingImages.length, selectedFiles.length]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    },
    maxFiles: maxFiles - existingImages.length,
    disabled: uploadMutation.isPending
  });

  const removePreviewImage = (index: number) => {
    // Revoke the object URL to prevent memory leaks
    URL.revokeObjectURL(previewImages[index]);
    
    setPreviewImages(prev => prev.filter((_, i) => i !== index));
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;

    try {
      const result = await uploadMutation.mutateAsync(selectedFiles);
      
      // Clean up preview URLs
      previewImages.forEach(url => URL.revokeObjectURL(url));
      setPreviewImages([]);
      setSelectedFiles([]);
      
      // Notify parent component
      if (onImagesUploaded) {
        onImagesUploaded(result.urls);
      }
    } catch (error) {
      console.error('Upload failed:', error);
    }
  };

  const totalImages = existingImages.length + selectedFiles.length;

  return (
    <VStack gap={4} align="stretch">
      {/* Upload Area */}
      <Box
        {...getRootProps()}
        border="2px dashed"
        borderColor={isDragActive ? "blue.400" : "gray.300"}
        borderRadius="lg"
        p={8}
        textAlign="center"
        cursor="pointer"
        bg={isDragActive ? "blue.50" : "gray.50"}
        transition="all 0.2s"
        _hover={{
          borderColor: "blue.400",
          bg: "blue.50"
        }}
      >
        <input {...getInputProps()} />
        <VStack gap={3}>
          <Box color="gray.500">
            <Upload size={48} />
          </Box>
          <VStack gap={1}>
            <Text fontWeight="medium" color="gray.700">
              {isDragActive ? "Drop images here" : "Click to upload or drag and drop"}
            </Text>
            <Text fontSize="sm" color="gray.500">
              PNG, JPG, JPEG, WebP up to 5MB each
            </Text>
            <Text fontSize="sm" color="gray.500">
              {totalImages}/{maxFiles} images selected
            </Text>
          </VStack>
        </VStack>
      </Box>

      {/* Upload Progress */}
      {uploadMutation.isPending && (
        <Box>
          <Text fontSize="sm" mb={2}>Uploading images...</Text>
          <Box bg="blue.100" borderRadius="md" overflow="hidden">
            <Box
              bg="blue.500"
              height="4px"
              animation="pulse 2s infinite"
              width="100%"
            />
          </Box>
        </Box>
      )}

      {/* Error Display */}
      {uploadMutation.isError && (
        <Box bg="red.50" border="1px solid" borderColor="red.200" borderRadius="md" p={3}>
          <Text color="red.600" fontSize="sm">
            Upload failed: {uploadMutation.error?.message || 'Unknown error'}
          </Text>
        </Box>
      )}

      {/* Existing Images */}
      {existingImages.length > 0 && (
        <Box>
          <Text fontWeight="medium" mb={3}>Existing Images</Text>
          <Grid templateColumns="repeat(auto-fill, minmax(120px, 1fr))" gap={3}>
            {existingImages.map((url, index) => (
              <Box key={index} position="relative">
                <Image
                  src={url}
                  alt={`Existing image ${index + 1}`}
                  borderRadius="md"
                  objectFit="cover"
                  w="full"
                  h="120px"
                />
                {onRemoveImage && (
                  <IconButton
                    aria-label="Remove image"
                    size="sm"
                    position="absolute"
                    top={1}
                    right={1}
                    colorScheme="red"
                    variant="solid"
                    onClick={() => onRemoveImage(index)}
                  >
                    <X size={16} />
                  </IconButton>
                )}
              </Box>
            ))}
          </Grid>
        </Box>
      )}

      {/* Preview Images */}
      {previewImages.length > 0 && (
        <Box>
          <Text fontWeight="medium" mb={3}>New Images to Upload</Text>
          <Grid templateColumns="repeat(auto-fill, minmax(120px, 1fr))" gap={3}>
            {previewImages.map((url, index) => (
              <Box key={index} position="relative">
                <Image
                  src={url}
                  alt={`Preview ${index + 1}`}
                  borderRadius="md"
                  objectFit="cover"
                  w="full"
                  h="120px"
                />
                <IconButton
                  aria-label="Remove preview"
                  size="sm"
                  position="absolute"
                  top={1}
                  right={1}
                  colorScheme="red"
                  variant="solid"
                  onClick={() => removePreviewImage(index)}
                >
                  <X size={16} />
                </IconButton>
              </Box>
            ))}
          </Grid>
        </Box>
      )}

      {/* Upload Button */}
      {selectedFiles.length > 0 && (
        <HStack justify="space-between">
          <Text fontSize="sm" color="gray.600">
            {selectedFiles.length} image(s) ready to upload
          </Text>
          <Button
            colorScheme="blue"
            onClick={handleUpload}
            loading={uploadMutation.isPending}
            loadingText="Uploading..."
          >
            <ImageIcon size={16} />
            Upload Images
          </Button>
        </HStack>
      )}
    </VStack>
  );
};

export default ImageUpload;
