import { Field } from "@chakra-ui/react";
import React from "react";
import Select, {
    SingleValue,
    MultiValue,
    ActionMeta,
    StylesConfig
} from 'react-select';

export interface SelectOption {
    value: string;
    label: string;
    isDisabled?: boolean;
}

interface FormSelectFieldProps {
    label?: string;
    description?: string;
    required?: boolean;
    errorText?: string;
    placeholder?: string;
    invalid?: boolean;
    options: SelectOption[];
    onChange?: (
        newValue: SingleValue<SelectOption> | MultiValue<SelectOption>,
        actionMeta: ActionMeta<SelectOption>
    ) => void;
    defaultValue?: SelectOption | SelectOption[];
    value?: SelectOption | SelectOption[];
    width?: string;
    name?: string;
    disabled?: boolean;
    isMulti?: boolean;
    isClearable?: boolean;
    isSearchable?: boolean;
    menuPortalTarget?: HTMLElement | null;
}

const FormSelectField = React.forwardRef<HTMLDivElement, FormSelectFieldProps>(
    (
        {
            label,
            description,
            required = false,
            errorText,
            placeholder = "Select an option",
            invalid = false,
            options,
            onChange,
            defaultValue,
            value,
            width = "100%",
            name,
            disabled = false,
            isMulti = false,
            isClearable = true,
            isSearchable = true,
            menuPortalTarget
        },
        ref
    ) => {
        const customStyles: StylesConfig<SelectOption, boolean> = {
            control: (baseStyles) => ({
                ...baseStyles,
                borderColor: "#e5e7eb",
                boxShadow: "none",
                padding: "4px 6px",
                borderRadius: "5px",
            }),
            container: (baseStyles) => ({
                ...baseStyles,
                width: width,
            }),
            option: (baseStyles, state) => ({
                ...baseStyles,
                backgroundColor:
                    state.isSelected
                        ? "#EDF2F7"
                        : "#fff",
                color: "#2D3748",
                "&:hover": {
                    backgroundColor: "#EDF2F7",
                },
                "&:active": {
                    backgroundColor: "#EDF2F7",
                },
                '&[data-selected="true"]': {
                    backgroundColor: "#EDF2F7",
                },
            }),
            menu: (baseStyles) => ({
                ...baseStyles,
                border: "1px solid #e5e7eb",
                boxShadow:
                    "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
                zIndex: 9999,
            }),
        }

        return (
            <Field.Root required={required} invalid={invalid} ref={ref}>
                {label && (
                    <Field.Label fontWeight="bold" color="gray.600">
                        {label}
                        {required && <Field.RequiredIndicator>*</Field.RequiredIndicator>}
                    </Field.Label>
                )}
                {description && (
                    <Field.Label color="gray.500" fontSize="sm">
                        {description}
                    </Field.Label>
                )}
                <Select
                    name={name}
                    options={options}
                    placeholder={placeholder}
                    isDisabled={disabled}
                    isMulti={isMulti}
                    isClearable={isClearable}
                    isSearchable={isSearchable}
                    value={value}
                    defaultValue={defaultValue}
                    onChange={onChange}
                    styles={customStyles}
                    menuPortalTarget={menuPortalTarget}
                    menuPosition="fixed"
                />
                {errorText && <Field.ErrorText>{errorText}</Field.ErrorText>}
            </Field.Root>
        );
    }
);

FormSelectField.displayName = "FormSelectField";

export default FormSelectField;
