import { Field, Input, HStack } from "@chakra-ui/react";
import React from "react";

interface FormDateTimePickerProps {
  label: string;
  description?: string;
  required?: boolean;
  errorText?: string;
  value?: string;
  onChange?: (value: string) => void;
  name?: string;
  invalid?: boolean;
  min?: string;
  max?: string;
}

const FormDateTimePicker = React.forwardRef<HTMLInputElement, FormDateTimePickerProps>(
  (
    {
      label,
      description,
      required = false,
      errorText,
      value,
      onChange,
      name,
      invalid = false,
      min,
      max,
    },
    ref
  ) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange?.(e.target.value);
    };

    return (
      <Field.Root required={required} invalid={invalid}>
        <Field.Label fontWeight="bold" color="gray.600">
          {label}
          {required && <Field.RequiredIndicator>*</Field.RequiredIndicator>}
        </Field.Label>
        {description && (
          <Field.Label color={"gray.500"} fontSize="sm">
            {description}
          </Field.Label>
        )}
        <Input
          name={name}
          type="datetime-local"
          borderWidth={1}
          borderColor={invalid ? "red.500" : "gray.300"}
          value={value}
          onChange={handleChange}
          ref={ref}
          min={min}
          max={max}
          _focus={{
            borderColor: "blue.400",
            boxShadow: "0 0 0 1px var(--chakra-colors-blue-400)",
          }}
        />
        {errorText && <Field.ErrorText>{errorText}</Field.ErrorText>}
      </Field.Root>
    );
  }
);

FormDateTimePicker.displayName = "FormDateTimePicker";

export default FormDateTimePicker;
