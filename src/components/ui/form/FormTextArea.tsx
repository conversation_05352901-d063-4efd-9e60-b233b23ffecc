import { Field, Textarea } from "@chakra-ui/react";
import React from "react";

interface FormTextAreaProps {
  label: string;
  description?: string;
  required?: boolean;
  errorText?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  name?: string;
  invalid?: boolean;
  rows?: number;
  maxLength?: number;
  resize?: "none" | "both" | "horizontal" | "vertical";
}

const FormTextArea = React.forwardRef<HTMLTextAreaElement, FormTextAreaProps>(
  (
    {
      label,
      description,
      required = false,
      errorText,
      placeholder = "",
      value,
      onChange,
      name,
      invalid = false,
      rows = 4,
      maxLength,
      resize = "vertical",
    },
    ref
  ) => {
    return (
      <Field.Root required={required} invalid={invalid}>
        <Field.Label fontWeight="bold" color="gray.600">
          {label}
          {required && <Field.RequiredIndicator>*</Field.RequiredIndicator>}
        </Field.Label>
        {description && (
          <Field.Label color={"gray.500"} fontSize="sm">
            {description}
          </Field.Label>
        )}
        <Textarea
          name={name}
          placeholder={placeholder}
          borderWidth={1}
          borderColor={invalid ? "red.500" : "gray.300"}
          value={value}
          onChange={onChange}
          ref={ref}
          rows={rows}
          maxLength={maxLength}
          resize={resize}
          _focus={{
            borderColor: "blue.400",
            boxShadow: "0 0 0 1px var(--chakra-colors-blue-400)",
          }}
        />
        {maxLength && value && (
          <Field.HelperText textAlign="right" fontSize="xs" color="gray.500">
            {value.length}/{maxLength}
          </Field.HelperText>
        )}
        {errorText && <Field.ErrorText>{errorText}</Field.ErrorText>}
      </Field.Root>
    );
  }
);

FormTextArea.displayName = "FormTextArea";

export default FormTextArea;
