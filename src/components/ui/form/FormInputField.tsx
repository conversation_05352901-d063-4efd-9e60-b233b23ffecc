import { Field, Input } from "@chakra-ui/react";
import { PasswordInput } from "../password-input";
import React from "react";

interface FormInputFieldProps {
  label: string;
  description?: string;
  required?: boolean;
  errorText?: string;
  placeholder?: string;
  type?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  name?: string;
  invalid?: boolean;
}

const FormInputField = React.forwardRef<HTMLInputElement, FormInputFieldProps>(
  (
    {
      label,
      description,
      required = false,
      errorText,
      placeholder = "",
      type = "text",
      value,
      onChange,
      name,
      invalid = false,
    },
    ref
  ) => {
    const isPassword = type === "password";

    return (
      <Field.Root required={required} invalid={invalid}>
        <Field.Label fontWeight="bold" color="gray.600">
          {label}
          {required && <Field.RequiredIndicator>*</Field.RequiredIndicator>}
        </Field.Label>
        {description && (
          <Field.Label  color={"gray.500"}>{description}</Field.Label>
        )}
        {isPassword ? (
          <PasswordInput
            name={name}
            placeholder={placeholder}
            borderWidth={1}
            borderColor={invalid ? "red.500" : "gray.300"}
            value={value}
            onChange={onChange}
            ref={ref}
          />
        ) : (
          <Input
            name={name}
            type={type}
            placeholder={placeholder}
            borderWidth={1}
            borderColor={invalid ? "red.500" : "gray.300"}
            value={value}
            onChange={onChange}
            ref={ref}
          />
        )}
        {errorText && <Field.ErrorText>{errorText}</Field.ErrorText>}
      </Field.Root>
    );
  }
);

FormInputField.displayName = "FormInputField";

export default FormInputField;
