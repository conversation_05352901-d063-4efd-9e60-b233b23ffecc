'use client'

import { Box, BoxProps } from '@chakra-ui/react'
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa'
import { useHorizontalScroll } from '@/hooks/useHorizontalScroll'

interface ScrollableContainerProps extends BoxProps {
  children: React.ReactNode
  showScrollButtons?: boolean
  scrollAmount?: number
  dragMultiplier?: number
  gap?: BoxProps['gap']
  buttonPosition?: 'inside' | 'outside'
  buttonSize?: string
  buttonOffset?: string
}

const ScrollableContainer: React.FC<ScrollableContainerProps> = ({
  children,
  showScrollButtons = true,
  scrollAmount = 300,
  dragMultiplier = 2,
  gap = { base: 3, md: 6 },
  buttonPosition = 'outside',
  buttonSize = '36px',
  buttonOffset = '-20px',
  ...boxProps
}) => {
  const {
    containerRef,
    isDragging,
    showLeftButton,
    showRightButton,
    scroll,
    handlers
  } = useHorizontalScroll({ scrollAmount, dragMultiplier })

  return (
    <Box position="relative" h="full" {...boxProps}>
      {showScrollButtons && showLeftButton && (
        <ScrollButton 
          position="left" 
          onClick={() => scroll('left')}
          buttonPosition={buttonPosition}
          buttonSize={buttonSize}
          buttonOffset={buttonOffset}
        />
      )}
      
      <Box
        ref={containerRef}
        display="flex"
        alignItems="start"
        overflowX="auto"
        overflowY="hidden"
        gap={gap}
        css={{
          '&::-webkit-scrollbar': { display: 'none' },
          msOverflowStyle: 'none',
          scrollbarWidth: 'none',
        }}
        userSelect="none"
        cursor={isDragging ? 'grabbing' : 'grab'}
        {...handlers}
      >
        {children}
      </Box>
      
      {showScrollButtons && showRightButton && (
        <ScrollButton 
          position="right" 
          onClick={() => scroll('right')}
          buttonPosition={buttonPosition}
          buttonSize={buttonSize}
          buttonOffset={buttonOffset}
        />
      )}
    </Box>
  )
}

interface ScrollButtonProps {
  position: 'left' | 'right'
  onClick: () => void
  buttonPosition: 'inside' | 'outside'
  buttonSize: string
  buttonOffset: string
}

const ScrollButton: React.FC<ScrollButtonProps> = ({
  position,
  onClick,
  buttonPosition,
  buttonSize,
  buttonOffset,
}) => {
  const isLeft = position === 'left'
  const isOutside = buttonPosition === 'outside'
  
  return (
    <Box
      position="absolute"
      left={isLeft ? (isOutside ? { base: '0', lg: buttonOffset } : '10px') : undefined}
      right={!isLeft ? (isOutside ? { base: '0', lg: buttonOffset } : '10px') : undefined}
      top="38%"
      transform="translateY(-50%)"
      zIndex="1"
    >
      <Box
        cursor="pointer"
        bg="white"
        boxShadow="lg"
        borderRadius="full"
        borderWidth="1px"
        borderColor="gray.200"
        h={buttonSize}
        w={buttonSize}
        display="flex"
        justifyContent="center"
        alignItems="center"
        onClick={onClick}
        color="gray.600"
        _hover={{
          bg: 'gray.50',
          transform: 'scale(1.05)',
        }}
        transition="all 0.2s"
      >
        {isLeft ? <FaChevronLeft size="16" /> : <FaChevronRight size="16" />}
      </Box>
    </Box>
  )
}

export default ScrollableContainer
