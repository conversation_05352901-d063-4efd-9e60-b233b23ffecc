"use client"

import { Box, Text, VStack } from "@chakra-ui/react"
import * as React from "react"

interface FormControlProps {
  isInvalid?: boolean
  children: React.ReactNode
  [key: string]: any
}

interface FormLabelProps {
  children: React.ReactNode
  [key: string]: any
}

interface FormErrorMessageProps {
  children: React.ReactNode
  [key: string]: any
}

export function FormControl({ isInvalid, children, ...props }: FormControlProps) {
  return (
    <Box {...props}>
      {children}
    </Box>
  )
}

export function FormLabel({ children, ...props }: FormLabelProps) {
  return (
    <Text
      as="label"
      fontSize="sm"
      fontWeight="medium"
      color="gray.700"
      mb={2}
      display="block"
      {...props}
    >
      {children}
    </Text>
  )
}

export function FormErrorMessage({ children, ...props }: FormErrorMessageProps) {
  return (
    <Text
      fontSize="sm"
      color="red.500"
      mt={1}
      {...props}
    >
      {children}
    </Text>
  )
}
