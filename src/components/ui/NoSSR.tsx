"use client"
import dynamic from 'next/dynamic'
import { ReactNode } from 'react'

interface NoSSRProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * NoSSR component using dynamic import to prevent SSR
 * More performant alternative to ClientOnly for components that should never be server-rendered
 */
const NoSSR: React.FC<NoSSRProps> = ({ children, fallback = null }) => {
  return <>{children}</>;
};

// Export as dynamic component with no SSR
export default dynamic(() => Promise.resolve(NoSSR), {
  ssr: false,
  loading: () => null
});
