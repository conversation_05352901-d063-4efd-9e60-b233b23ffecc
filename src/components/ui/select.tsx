// "use client"

// import { Select as ChakraSelect, SelectProps } from "@chakra-ui/react"
// import * as React from "react"

// export interface CustomSelectProps extends Omit<SelectProps, 'children'> {
//   placeholder?: string
//   children: React.ReactNode
// }

// export const Select = React.forwardRef<HTMLSelectElement, CustomSelectProps>(
//   ({ placeholder, children, ...props }, ref) => {
//     return (
//       <ChakraSelect ref={ref} {...props}>
//         {placeholder && (
//           <option value="" disabled>
//             {placeholder}
//           </option>
//         )}
//         {children}
//       </ChakraSelect>
//     )
//   }
// )

// Select.displayName = "Select"
