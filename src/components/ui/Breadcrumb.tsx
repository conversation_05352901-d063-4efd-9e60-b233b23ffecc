'use client'

import { Box, Breadcrumb as ChakraBreadcrumb, Text } from '@chakra-ui/react'
import Link from 'next/link'

export interface BreadcrumbItem {
  label: string
  href?: string
  isCurrentPage?: boolean
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items
}) => {
  return (
    <ChakraBreadcrumb.Root whiteSpace={"nowrap"}>
      <ChakraBreadcrumb.List>
        {items.map((item, index) => (
          <Box alignItems="center" gap={3} display="flex" key={index}>
            {item.isCurrentPage ? (
              <ChakraBreadcrumb.Item>
                <ChakraBreadcrumb.CurrentLink>
                  <Text lineClamp={1} maxW={"600px"} fontSize="sm" fontWeight="bold" color="gray.600">
                    {item.label}
                  </Text>
                </ChakraBreadcrumb.CurrentLink>
              </ChakraBreadcrumb.Item>
            ) : (
              <ChakraBreadcrumb.Item>
                <ChakraBreadcrumb.Link href={item.href}>{item.label}</ChakraBreadcrumb.Link>
              </ChakraBreadcrumb.Item>
            )}
            {index < items.length - 1 && (
              <ChakraBreadcrumb.Separator />
            )}
          </Box>
        ))}
      </ChakraBreadcrumb.List>
    </ChakraBreadcrumb.Root >
  )
}

export default Breadcrumb
