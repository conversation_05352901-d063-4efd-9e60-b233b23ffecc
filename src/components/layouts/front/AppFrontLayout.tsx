"use client";
import React from 'react'
import "@fontsource/poppins"
import { Box, Center, Spinner } from '@chakra-ui/react';
import NavbarFront from './navbar/Navbar';
import FooterFront from './footer/Footer';
import { usePathname } from 'next/navigation';
import { AuthProvider } from '@/components/auth/AuthProvider';
import { Toaster } from '@/components/ui/toaster';
import { useAppStore } from '@/stores/app/store';
// import { useSearchParams } from 'next/navigation';

const AppFrontLayout = (props: {
  children: React.ReactNode;
}) => {
  // const searchParams = useSearchParams()
  const { children } = props;
  const pathname = usePathname()
  const { isLoading } = useAppStore()
  const pathWithoutLocale = pathname?.replace(/^\/(en|id)(\/|$)/, '/');
  const isShowingNavbar = ['/auth/login', '/auth/register',].includes(pathWithoutLocale);
  const isShowingFooter = ['/auth/login', '/auth/register', '/selling'].includes(pathWithoutLocale);
  return (
    <AuthProvider>
      <Box as={"main"} bg="#FEFEFE" minH="100vh" position={"relative"}>
        {
          isShowingNavbar ? null : <NavbarFront />
        }
        <Box bg="gray.100" py={isShowingNavbar ? 0 : 2}>
          {children}
        </Box>
        {
          isShowingFooter ? null : <FooterFront />
        }
      </Box>
      <Toaster />
      {
        isLoading &&
        <Box pos="absolute" inset="0" bg="bg/80">
          <Center h="full">
            <Spinner
              size="xl"
              color="black" />
          </Center>
        </Box>
      }

    </AuthProvider>

  )
}

export default AppFrontLayout
