"use client"
import NavbarStyles from './navbar.module.css'
import {
    Box,
    Button,
    Container,
    Flex,
    HStack,
    Input,
    InputGroup,
    HoverCard,
    Portal,
    Stack,
    Text,
    createListCollection,
    Avatar,
    Icon,
    Skeleton,
    Drawer,
    VStack,
    CloseButton,
} from '@chakra-ui/react'
import Image from 'next/image'
import Link from 'next/link'
import { Link as ChakraLink } from "@chakra-ui/react"
import React, { useEffect, useRef, useState } from 'react'
import { LuSearch } from 'react-icons/lu'
import { IoGlobeOutline, IoLogOut, IoMenu } from 'react-icons/io5'
import CartIcon from '@/components/cart/CartIcon'
import { ChakraSelect } from '@/components/ui/select/ChakraSelect'
import { useTranslations } from 'next-intl'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import { useParams, usePathname, useRouter } from 'next/navigation'
import { signOut, useSession } from 'next-auth/react'
import { FaAngleDown, FaAngleRight } from 'react-icons/fa'

type MenuListType = {
    label: string;
    href: string;
    children?: MenuListType[];
}

export const MenuList: MenuListType[] = [
    {
        label: "Auction",
        href: "/marketplace?sellType=auction",
        children: [
            {
                label: "Live Auction",
                href: "/marketplace?type=live-auction"
            },
            {
                label: "Upcoming Auction",
                href: "/marketplace?type=upcoming-auction"
            },
            {
                label: "Past Auction",
                href: "/marketplace?type=past-auction"
            }
        ]
    },
    {
        label: "Buy Now",
        href: "/marketplace?sellType=buy-now",
    },
    {
        label: "Trading Cards",
        href: "/marketplace?type=trading-cards",
        children: [
            {
                label: "Trading Card Sets",
                href: "/marketplace?type=trading-card-sets"
            },
            {
                label: "Trading Card Series",
                href: "/marketplace?type=trading-card-series"
            }
        ]
    },
    {
        label: "More",
        href: "/more"
    },
]

type MenuProfileListType = {
    label: string;
    href: string;
    onClick?: (event: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => void;
}

const MenuProfileList: MenuProfileListType[] = [
    {
        label: "Summary",
        href: "/account/summary"
    },
    {
        label: "Account",
        href: "/account/setting"
    },
    {
        label: "Selling",
        href: "/account/selling"
    },
    {
        label: "Buying",
        href: "/account/buying"
    },
    {
        label: "Bidding",
        href: "/account/bidding"
    },
    {
        label: "Watchlist",
        href: "/account/watchlist"
    },
    {
        label: "Logout",
        href: "#",
        onClick: (e) => {
            e.preventDefault();
            signOut()
        }
    }
]

type MenuDropdownType = {
    [key: string]: boolean;
}

export default function NavbarFront() {
    const dataSession = useSession()
    const session = dataSession.data;
    const router = useRouter();
    const pathname = usePathname();
    const params = useParams();
    const { locale } = params;
    const t = useTranslations();
    const {
        currency,
        language,
        tempCurrency,
        tempLanguage,
        setTempCurrency,
        setTempLanguage,
        saveSettings,
        cancelSettings,
        hasUnsavedChanges,
        getCurrencyLabel,
        getLanguageLabel
    } = useCurrencyLanguage();
    const selectLanguangeCurrencyRef = useRef<HTMLDivElement>(null);
    const profileCardRef = useRef<HTMLDivElement>(null);
    const [openCardLanguangeCurrency, setOpenCardLanguangeCurrency] = useState(false);
    const [openCardProfile, setOpenCardProfile] = useState(false);
    const [openMenuDropdown, setOpenMenuDropdown] = useState<MenuDropdownType>({});
    const [mobileMenuDropdown, setMobileMenuDropdown] = useState<MenuDropdownType>({});
    const [openDrawer, setOpenDrawer] = useState(false);

    const countries = createListCollection({
        items: [
            { value: "id", label: "Bahasa Indonesia" },
            { value: "en", label: "English" },
        ],
        itemToValue: (item) => item.value,
    })

    const currencies = createListCollection({
        items: [
            { value: "idr", label: "IDR - Indonesia Dupiah" },
            { value: "usd", label: "USD - US Dollar" },
        ],
        itemToValue: (item) => item.value,
    })

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (selectLanguangeCurrencyRef.current && !selectLanguangeCurrencyRef.current.contains(event.target as Node)) {
                setOpenCardLanguangeCurrency(false);
            }

            if (profileCardRef.current && !profileCardRef.current.contains(event.target as Node)) {
                setOpenCardProfile(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [selectLanguangeCurrencyRef]);

    const handleSubmitLanguageCurrency = async () => {
        // Save the settings
        await saveSettings();
        // Close the dropdown after saving
        setOpenCardLanguangeCurrency(false);
    };

    const handleCancelLanguageCurrency = () => {
        // Cancel changes and close dropdown
        cancelSettings();
        setOpenCardLanguangeCurrency(false);
    };

    const toggleMobileMenuDropdown = (label: string) => {
        setMobileMenuDropdown(prev => ({
            ...prev,
            [label]: !prev[label]
        }));
    };

    const onOpen = () => {
        setOpenDrawer(true);
    };
    const onClose = () => {
        setOpenDrawer(false);
    };

    return (
        <Box
            as="header"
            position="sticky"
            top="0"
            zIndex="1000"
            backdropFilter={"blur(100px)"}
            boxShadow="rgba(0, 0, 0, 0.04) 0px 3px 12px"
            backgroundColor="rgba(255, 255, 255, 0.8)"
        >
            <Box
                maxW="100%"
                px={4}
                py={3}
                >
                <Flex gap={{
                    base: 2,
                    lg: 6
                }} align="center">
                    <Flex justify="space-between" align="center" flex={{ base: 1, lg: 'none' }}>
                        <Button
                            onClick={onOpen}
                            variant="plain" display={{ base: "flex", lg: "none" }} p={2}>
                            <Image
                                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIBAMAAACnw650AAAAG1BMVEUjIyMvLy////+Xl5f4+Pjz8/Ourq6GhoalpaWPtzYJAAAAZklEQVRIx2NQwgBOGCIMSkSAQakolAjAYGxsbG5sXIyXYAACQQZGAXzEEAaCRACGNCIAgwsRYOimp1FFVFREVFIpJwIQl3yHd+a0IAKMJrpRRdSuOQmCAcgwAgQg3TNnBxFgeNecAHACSX1/gu7fAAAAAElFTkSuQmCC"
                                alt="hamburger"
                                width={20}
                                height={20}
                            />
                        </Button>
                        <Link
                            style={{
                                width: "100%",
                            }}
                            href='/'>
                            <Image
                                src="/logo.png"
                                alt="Logo"
                                width={100}
                                height={80}
                                objectFit="cover"
                                className={NavbarStyles.navbar_logo}
                            />
                        </Link>
                    </Flex>
                    <Flex
                        width="full"
                        gap={{
                            base: 2,
                            lg: 6
                        }}
                        align="center">
                        <Box
                            as={'form'}
                            onSubmit={(e) => {
                                e.preventDefault();
                                const searchValue = (e.target as HTMLFormElement).search.value;
                                console.log(searchValue);
                                if (searchValue) {
                                    router.push(`/${locale}/marketplace?keyword=${searchValue}`);
                                }
                            }}
                            width={{
                                base: "100%",
                                lg: "260px",
                                xl: "340px",
                            }}
                        >
                            <InputGroup flex="1" startElement={<LuSearch size={22} />}>
                                <Input
                                    ps={12}
                                    placeholder={t('Navbar.placeholderSearch')}
                                    borderRadius="full"
                                    height="45px"
                                    bg="white"
                                    name='search'
                                    borderColor="gray.300"
                                    _hover={{ bg: 'gray.100' }}
                                    _focus={{ bg: 'white', borderColor: 'gray.200' }}
                                />
                            </InputGroup>
                        </Box>
                        <HStack
                            justify="start"
                            display={{
                                base: "none",
                                lg: "flex"
                            }}
                            gap={8}>
                            {MenuList.map((item, index) =>
                                (item.children?.length ?? 0) > 0 ? (
                                    <HoverCard.Root
                                        key={index}
                                        onOpenChange={(e) => {
                                            setOpenMenuDropdown((prev) => ({
                                                ...prev,
                                                [item.label]: e.open
                                            }));
                                        }}
                                        size="sm"
                                        open={openMenuDropdown[item.label] ?? false}>
                                        <HoverCard.Trigger asChild>
                                            <Box
                                                textTransform="capitalize"
                                                fontSize={"sm"}
                                                color="gray.700"
                                                fontWeight="semibold"
                                                cursor={"pointer"}
                                                whiteSpace="nowrap"
                                                onClick={() => {
                                                    setOpenMenuDropdown((prev) => ({
                                                        ...prev,
                                                        [item.label]: !prev[item.label]
                                                    }));
                                                }}
                                            >
                                                {item.label}
                                                <Box
                                                    as="span"
                                                    ml={1}
                                                    fontSize="xs"
                                                    color="gray.500"
                                                    display="inline-block"
                                                    transform={openMenuDropdown[item.label] ? "rotate(180deg)" : "rotate(0deg)"}
                                                    transition="transform 0.2s ease-in-out"
                                                    className={NavbarStyles.dropdownIcon}>
                                                    <Icon
                                                        as={FaAngleDown}
                                                        boxSize={4}
                                                        color="gray.800"
                                                    />
                                                </Box>
                                            </Box>
                                        </HoverCard.Trigger>
                                        <Portal>
                                            <HoverCard.Positioner placeContent={"center"}>
                                                <HoverCard.Content p={2} minW={240} maxW={300}>
                                                    <Stack gap={0}>
                                                        {item.children?.map((item, index) => (
                                                            <ChakraLink
                                                                key={index}
                                                                as={Link}
                                                                href={`/${locale}/${item.href}`}
                                                                textStyle="sm"
                                                                fontWeight="regular"
                                                                color="gray.800"
                                                                borderRadius={8}
                                                                _hover={{
                                                                    textDecoration: "none",
                                                                    bg: "gray.100"
                                                                }}
                                                                px={3}
                                                                py={2}
                                                            >
                                                                {item.label}
                                                            </ChakraLink>
                                                        ))}
                                                    </Stack>
                                                </HoverCard.Content>
                                            </HoverCard.Positioner>
                                        </Portal>
                                    </HoverCard.Root>
                                ) : (
                                    <ChakraLink
                                        key={index}
                                        textTransform="capitalize"
                                        fontSize={"sm"}
                                        color="gray.700"
                                        fontWeight="semibold"
                                        as={Link}
                                        href={item.href}
                                        whiteSpace="nowrap"
                                        _hover={{
                                            textDecoration: "none",
                                        }}
                                        _focus={{
                                            outline: "none",
                                        }}
                                    >
                                        {item.label}
                                    </ChakraLink>
                                )
                            )}
                        </HStack>
                        <HStack ms="auto" gap={2}>
                            <HoverCard.Root
                                onOpenChange={(e) => setOpenCardLanguangeCurrency(e.open)}
                                size="sm"
                                open={openCardLanguangeCurrency}>
                                <HoverCard.Trigger asChild>
                                    <Box
                                        display={{
                                            base: "none",
                                            md: "flex"
                                        }}
                                        alignItems="center"
                                        textStyle="sm"
                                        fontWeight="semibold"
                                        color="gray.700"
                                        borderRadius="full"
                                        px={3}
                                        py={2}
                                        _hover={{ bg: "gray.100" }}
                                        _active={{ bg: "gray.200" }}
                                        _focus={{ boxShadow: "outline" }}
                                        cursor={"pointer"}
                                        onClick={() => {
                                            setOpenCardLanguangeCurrency(!openCardLanguangeCurrency)
                                        }}
                                        whiteSpace="nowrap"
                                        fontSize={{
                                            base: 10,
                                            md: "sm"
                                        }}
                                    >
                                        <IoGlobeOutline size={20} />
                                        <Text
                                            fontWeight="semibold"
                                            color="gray.700"
                                            textTransform="uppercase"
                                            ml={2}>
                                            {language.toUpperCase()} - {currency}
                                        </Text>
                                    </Box>
                                </HoverCard.Trigger>
                                <Portal>
                                    <HoverCard.Positioner>
                                        <HoverCard.Content ref={selectLanguangeCurrencyRef}>
                                            <HoverCard.Arrow />
                                            <Stack gap="4">
                                                <Text
                                                    fontSize="sm"
                                                    fontWeight="bold"
                                                    color="gray.700"
                                                >
                                                    {t('Navbar.languageCurrencyChangeText')}
                                                </Text>

                                                <ChakraSelect
                                                    label={t('language')}
                                                    placeholder={`Pilih ${t('language')}`}
                                                    collection={countries}
                                                    defaultValue={[tempLanguage]}
                                                    portalRef={selectLanguangeCurrencyRef}
                                                    onValueChange={(e) => {
                                                        if (e.value && e.value.length > 0) {
                                                            setTempLanguage(e.value[0] as 'en' | 'id')
                                                        }
                                                    }}
                                                />

                                                <ChakraSelect
                                                    label={t('currency')}
                                                    placeholder={`Pilih ${t('currency')}`}
                                                    collection={currencies}
                                                    defaultValue={[tempCurrency.toLowerCase()]}
                                                    portalRef={selectLanguangeCurrencyRef}
                                                    onValueChange={(e) => {
                                                        if (e.value && e.value.length > 0) {
                                                            setTempCurrency(e.value[0].toUpperCase() as 'USD' | 'IDR')
                                                        }
                                                    }}
                                                />

                                                <HStack gap={2} width="full">
                                                    <Button
                                                        onClick={handleCancelLanguageCurrency}
                                                        borderRadius={12}
                                                        px={4}
                                                        fontWeight={"bold"}
                                                        size={"sm"}
                                                        variant="outline"
                                                        colorScheme="gray"
                                                        flex={1}
                                                        disabled={!hasUnsavedChanges}
                                                    >
                                                        Cancel
                                                    </Button>
                                                    <Button
                                                        onClick={handleSubmitLanguageCurrency}
                                                        borderRadius={12}
                                                        px={4}
                                                        fontWeight={"bold"}
                                                        size={"sm"}
                                                        variant="solid"
                                                        colorScheme={hasUnsavedChanges ? "blue" : "gray"}
                                                        flex={1}
                                                        disabled={!hasUnsavedChanges}
                                                    >
                                                        {hasUnsavedChanges ? t('save') : 'Saved'}
                                                    </Button>
                                                </HStack>
                                            </Stack>
                                        </HoverCard.Content>
                                    </HoverCard.Positioner>
                                </Portal>
                            </HoverCard.Root>

                            {dataSession.status !== "loading" && session && (
                                <CartIcon />
                            )}

                            {dataSession.status == "loading" ? (
                                <Skeleton
                                    width={{ base: "36px", md: "180px" }}
                                    height="36px"
                                    borderRadius="lg" />
                            ) : session ? (
                                <>
                                    <HoverCard.Root
                                        onOpenChange={(e) => setOpenCardProfile(e.open)}
                                        size="sm"
                                        open={openCardProfile}>
                                        <HoverCard.Trigger asChild>
                                            <Box
                                                display={"flex"}
                                                alignItems="center"
                                                textStyle="sm"
                                                fontWeight="semibold"
                                                color="gray.700"
                                                borderRadius="full"
                                                _hover={{ bg: "gray.100" }}
                                                _active={{ bg: "gray.200" }}
                                                _focus={{ boxShadow: "outline" }}
                                                cursor={"pointer"}
                                                onClick={() => {
                                                    setOpenCardProfile(!openCardProfile)
                                                }}
                                                whiteSpace="nowrap"
                                                fontSize={{
                                                    base: 10,
                                                    md: "md"
                                                }}
                                            >
                                                <Avatar.Root variant="outline" size="sm" borderWidth={2} borderColor="black">
                                                    <Avatar.Fallback name={session.user?.name ?? ""} />
                                                </Avatar.Root>
                                            </Box>
                                        </HoverCard.Trigger>
                                        <Portal>
                                            <HoverCard.Positioner placeContent={"center"}>
                                                <HoverCard.Content p={2} minW={240} maxW={300} ref={profileCardRef}>
                                                    <HStack gap={3} borderBottomWidth={1} borderColor="gray.200" px={3} pt={3} pb={4} alignItems="flex-start">
                                                        <Avatar.Root variant="outline" size="sm" borderWidth={2} borderColor="black">
                                                            <Avatar.Fallback name={session.user?.name ?? ""} />
                                                        </Avatar.Root>
                                                        <Box>
                                                            <Text
                                                                as={"div"}
                                                                fontWeight="bold"
                                                                color="gray.800"
                                                                fontSize="xs"
                                                            >
                                                                {session.user?.name ?? t('Navbar.unknownUser')}
                                                            </Text>
                                                            <Text
                                                                as={"div"}
                                                                fontWeight="regular"
                                                                color="gray.500"
                                                                fontSize="xs"
                                                            >
                                                                {session.user?.email ?? t('Navbar.unknownEmail')}
                                                            </Text>
                                                        </Box>
                                                    </HStack>
                                                    <Stack gap={0} mt={2}>
                                                        {MenuProfileList.map((item, index) => (
                                                            <ChakraLink
                                                                key={index}
                                                                as={Link}
                                                                href={`/${locale}/${item.href}`}
                                                                textStyle="sm"
                                                                fontWeight="regular"
                                                                color="gray.800"
                                                                borderRadius={8}
                                                                _hover={{
                                                                    textDecoration: "none",
                                                                    bg: "gray.100"
                                                                }}
                                                                px={3}
                                                                py={2}
                                                                onClick={item.onClick}
                                                            >
                                                                {item.label}
                                                                {item.label === "Logout" ? (
                                                                    <Icon
                                                                        as={IoLogOut}
                                                                        ml="auto"
                                                                        color="gray.500"
                                                                        boxSize={4}
                                                                    />
                                                                ) : null}
                                                            </ChakraLink>
                                                        ))}
                                                    </Stack>
                                                </HoverCard.Content>
                                            </HoverCard.Positioner>
                                        </Portal>
                                    </HoverCard.Root>
                                    <Button
                                        borderRadius="full"

                                        px={4}
                                        fontWeight={"bold"}
                                        textTransform="capitalize"
                                        display={{ base: "none", md: "block" }}
                                        size={"sm"}
                                        onClick={() => {
                                            router.push(`/${locale}/account/selling/create`);
                                        }}
                                    >
                                        {t('Button.sellNow')}
                                    </Button>
                                </>
                            ) : (
                                <>
                                    {/* Desktop Auth Buttons */}
                                    <Button
                                        borderRadius="full"
                                        px={4}
                                        fontWeight={"bold"}
                                        textTransform="capitalize"
                                        display={{ base: "none", md: "flex" }}
                                        size={"sm"}
                                        color={"gray.700"}
                                        borderColor={"gray.400"}
                                        variant="outline"
                                        onClick={() => {
                                            router.push(`/${locale}/auth/login`);
                                        }}
                                    >
                                        {t('Button.login')}
                                    </Button>
                                    <Button
                                        borderRadius="full"

                                        px={4}
                                        fontWeight={"bold"}
                                        textTransform="capitalize"
                                        display={{ base: "none", md: "block" }}
                                        size={"sm"}
                                        onClick={() => {
                                            router.push(`/${locale}/auth/register`);
                                        }}
                                    >
                                        {t('Button.sellNow')}
                                    </Button>

                                    {/* Mobile Auth Button */}
                                    <Button
                                        px={4}
                                        fontWeight={"bold"}
                                        textTransform="capitalize"
                                        borderRadius="full"
                                        display={{ base: "flex", md: "none" }}
                                        size={"sm"}
                                        onClick={() => {
                                            router.push(`/${locale}/auth/login`);
                                        }}
                                    >
                                        Login
                                    </Button>
                                </>
                            )}
                        </HStack>
                    </Flex>
                </Flex>
            </Box>

            {/* Mobile Drawer */}
            <Drawer.Root open={openDrawer} onOpenChange={(e) => setOpenDrawer(e.open)} >
                <Portal>
                    <Drawer.Backdrop />
                    <Drawer.Positioner>
                        <Drawer.Content>
                            <Drawer.CloseTrigger asChild>
                                <CloseButton size="sm" />
                            </Drawer.CloseTrigger>
                            <Drawer.Header borderBottomWidth="1px">
                                <Link href="/" onClick={onClose}>
                                    <Image
                                        src="/logo.png"
                                        alt="Logo"
                                        width={100}
                                        height={40}
                                        objectFit="contain"
                                    />
                                </Link>
                            </Drawer.Header>
                            <Drawer.Body>
                                <VStack align="stretch" gap={4} mt={4}>
                                    {MenuList.map((item, index) => (
                                        <Box key={index}>
                                            {(item.children?.length ?? 0) > 0 ? (
                                                <>
                                                    <Flex
                                                        justifyContent="space-between"
                                                        alignItems="center"
                                                        py={2}
                                                        cursor="pointer"
                                                        onClick={() => toggleMobileMenuDropdown(item.label)}
                                                    >
                                                        <Text fontWeight="semibold">{item.label}</Text>
                                                        <Icon
                                                            as={FaAngleRight}
                                                            transform={mobileMenuDropdown[item.label] ? "rotate(90deg)" : "rotate(0deg)"}
                                                            transition="transform 0.2s ease"
                                                        />
                                                    </Flex>
                                                    {mobileMenuDropdown[item.label] && (
                                                        <VStack pl={4} gap={2} align="stretch">
                                                            {item.children?.map((child, childIndex) => (
                                                                <ChakraLink
                                                                    key={childIndex}
                                                                    as={Link}
                                                                    href={`/${locale}${child.href}`}
                                                                    onClick={onClose}
                                                                    py={2}
                                                                    display="block"
                                                                >
                                                                    {child.label}
                                                                </ChakraLink>
                                                            ))}
                                                        </VStack>
                                                    )}
                                                </>
                                            ) : (
                                                <ChakraLink
                                                    as={Link}
                                                    href={`/${locale}${item.href}`}
                                                    onClick={onClose}
                                                    py={2}
                                                    display="block"
                                                    fontWeight="semibold"
                                                >
                                                    {item.label}
                                                </ChakraLink>
                                            )}
                                        </Box>
                                    ))}

                                    <Box as={"hr"} />

                                    {dataSession.status !== "loading" && (
                                        session ? (
                                            <>
                                                <Text fontWeight="bold" fontSize="lg">My Account</Text>
                                                <VStack pl={4} gap={2} align="stretch">
                                                    {MenuProfileList.map((item, index) => (
                                                        <ChakraLink
                                                            key={index}
                                                            as={Link}
                                                            href={`/${locale}${item.href}`}
                                                            onClick={(e) => {
                                                                onClose();
                                                                if (item.onClick) {
                                                                    item.onClick(e);
                                                                }
                                                            }}
                                                            py={2}
                                                            display="block"
                                                        >
                                                            {item.label}
                                                        </ChakraLink>
                                                    ))}
                                                </VStack>
                                                <Button
                                                    borderRadius="full"
                                                    mt={4}
                                                    onClick={() => {
                                                        onClose();
                                                        router.push(`/${locale}/account/selling/create`);
                                                    }}
                                                >
                                                    {t('Button.sellNow')}
                                                </Button>
                                            </>
                                        ) : (
                                            <>
                                                <Button
                                                    borderRadius="full"
                                                    onClick={() => {
                                                        onClose();
                                                        router.push(`/${locale}/auth/login`);
                                                    }}
                                                    variant="outline"
                                                >
                                                    {t('Button.login')}
                                                </Button>
                                                <Button
                                                    borderRadius="full"
                                                    onClick={() => {
                                                        onClose();
                                                        router.push(`/${locale}/auth/register`);
                                                    }}
                                                >
                                                    {t('Button.sellNow')}
                                                </Button>
                                            </>
                                        )
                                    )}
                                </VStack>
                            </Drawer.Body>
                        </Drawer.Content>
                    </Drawer.Positioner>
                </Portal>
            </Drawer.Root>

            {/* Mobile Profile Dropdown */}
            {openCardProfile && (
                <Box
                    position="fixed"
                    top="60px"
                    right="10px"
                    bg="white"
                    boxShadow="lg"
                    borderRadius="md"
                    zIndex="1001"
                    p={2}
                    minW="200px"
                    display={{ base: "block", md: "none" }}
                >
                    <VStack align="stretch">
                        {MenuProfileList.map((item, index) => (
                            <ChakraLink
                                key={index}
                                as={Link}
                                href={`/${locale}${item.href}`}
                                onClick={(e) => {
                                    setOpenCardProfile(false);
                                    if (item.onClick) {
                                        item.onClick(e);
                                    }
                                }}
                                py={2}
                                px={3}
                                borderRadius="md"
                                _hover={{ bg: "gray.100" }}
                            >
                                {item.label}
                            </ChakraLink>
                        ))}
                    </VStack>
                </Box>
            )}
        </Box>
    )
}