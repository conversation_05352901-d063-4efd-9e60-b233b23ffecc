import SliderStyles from './slider.module.css'
import React, { useRef } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination } from 'swiper/modules';
import SwiperCore from 'swiper';

import 'swiper/css';
import 'swiper/css/pagination';
import Image from 'next/image';

interface HeroSliderProps {
    images: string[];
}

export default function HeroSlider({
    images
}: HeroSliderProps) {

    const swiperRef = useRef<SwiperCore>(null);

    const handleImageLoad = () => {
        swiperRef.current?.update();
    };

    return (
        <Swiper
            pagination={{
                clickable: true,
                renderBullet: (index, className) => {
                    return `<span class="${className}" style="background-color:rgb(255, 255, 255);"></span>`;
                }
            }}
            slidesPerView={1.2}
            spaceBetween={10}
            modules={[Pagination]}
            onSwiper={(swiper) => (swiperRef.current = swiper)}
        >
            {
                images.map((image, index) => (
                    <SwiperSlide key={index} className={SliderStyles.slider_hero__slide}>
                        <Image
                            width={1100}
                            height={460}
                            src={image}
                            priority
                            sizes="100vw"
                            alt="Green double couch with wooden legs"
                            className={SliderStyles.slider_hero__img}
                            onLoad={handleImageLoad}
                        />
                    </SwiperSlide>
                ))
            }

        </Swiper>
    )
}
