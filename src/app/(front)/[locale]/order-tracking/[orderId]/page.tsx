'use client'
import React, { useState } from 'react'
import { useParams } from 'next/navigation'
import {
  Box,
  Container,
  VStack,
  HStack,
  Text,
  Heading,
  Card,
  Badge,
  Grid,
  GridItem,
  Image,
  Button,
  Spinner,
  Icon,
  Separator,
  Alert,
  Progress,
  Flex,
  Link,
  Tooltip
} from '@chakra-ui/react'
import {
  FaCheckCircle,
  FaClock,
  FaShippingFast,
  FaBox,
  FaMapMarkerAlt,
  FaCreditCard,
  FaPhone,
  FaEnvelope,
  FaCopy,
  FaTruck,
  FaStore,
  FaCalendarAlt,
  FaInfoCircle,
  FaExternalLinkAlt,
  FaDownload,
  FaReceipt,
  FaUndo,
  FaExclamationTriangle,
  FaShieldAlt,
  FaHeadset,
  FaGift,
  FaStar,
  FaCheck
} from 'react-icons/fa'

import { useOrderTrackingQuery } from '@/services/useOrderQuery'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import { toaster } from '@/components/ui/toaster'
import { formatDistanceToNow, format } from 'date-fns'

const OrderTrackingPage = () => {
  const params = useParams()
  const orderId = params.orderId as string
  const { formatPrice } = useCurrencyLanguage()
  const [showFullTimeline, setShowFullTimeline] = useState(false)

  const { data: orderData, isLoading, error } = useOrderTrackingQuery(orderId)

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text)
    toaster.create({
      title: "Copied!",
      description: `${type} copied to clipboard`,
      type: "success",
      duration: 2000,
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending_payment': return 'orange'
      case 'paid': return 'blue'
      case 'seller_confirmed': return 'purple'
      case 'shipped': return 'cyan'
      case 'delivered': return 'green'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending_payment': return 'Waiting for Payment'
      case 'paid': return 'Payment Confirmed'
      case 'seller_confirmed': return 'Seller Confirmed'
      case 'shipped': return 'Shipped'
      case 'delivered': return 'Delivered'
      case 'cancelled': return 'Cancelled'
      default: return status.replace('_', ' ').toUpperCase()
    }
  }

  const getStatusDescription = (status: string) => {
    switch (status) {
      case 'pending_payment': return 'Please complete your payment to proceed with the order'
      case 'paid': return 'Your payment has been confirmed and the seller has been notified'
      case 'seller_confirmed': return 'The seller has confirmed your order and is preparing for shipment'
      case 'shipped': return 'Your order is on its way to your delivery address'
      case 'delivered': return 'Your order has been successfully delivered'
      case 'cancelled': return 'This order has been cancelled'
      default: return 'Order status updated'
    }
  }

  const getStatusIcon = (status: string, isCompleted: boolean, isCurrent: boolean) => {
    const iconProps = {
      size: '20px',
      color: isCompleted ? 'green.500' : isCurrent ? 'blue.500' : 'gray.400'
    }

    switch (status) {
      case 'pending_payment': return <FaCreditCard {...iconProps} />
      case 'paid': return <FaCheckCircle {...iconProps} />
      case 'seller_confirmed': return <FaBox {...iconProps} />
      case 'shipped': return <FaTruck {...iconProps} />
      case 'delivered': return <FaBox {...iconProps} />
      default: return <FaClock {...iconProps} />
    }
  }

  if (isLoading) {
    return (
      <Container maxW="6xl" py={8}>
        <VStack gap={8} align="center">
          <Spinner size="xl" />
          <Text>Loading order tracking...</Text>
        </VStack>
      </Container>
    )
  }

  if (error || !orderData?.order) {
    return (
      <Container maxW="6xl" py={8}>
        <Box p={4} bg="red.50" borderRadius="md" border="1px" borderColor="red.200">
          <Text fontWeight="bold" color="red.700">Order Not Found</Text>
          <Text fontSize="sm" color="red.600">
            The order you're looking for doesn't exist or you don't have permission to view it.
          </Text>
        </Box>
      </Container>
    )
  }

  const order = orderData.order

  return (
    <Container maxW="6xl" py={8}>
      <VStack gap={8} align="stretch">
        {/* Header */}
        <Card.Root>
          <Card.Body>
            <VStack gap={6} align="stretch">
              <HStack justify="space-between" align="start">
                <VStack align="start" gap={2}>
                  <Heading size="lg">Order Tracking</Heading>
                  <HStack gap={2}>
                    <Text fontSize="sm" color="gray.600">Invoice:</Text>
                    <Text fontWeight="bold">{order.orderNumber}</Text>
                    <Button
                      size="xs"
                      variant="ghost"
                      onClick={() => copyToClipboard(order.orderNumber, "Order number")}
                    >
                      <FaCopy />
                    </Button>
                  </HStack>
                  {order.trackingNumber && (
                    <HStack gap={2}>
                      <Text fontSize="sm" color="gray.600">Tracking:</Text>
                      <Text fontWeight="bold">{order.trackingNumber}</Text>
                      <Button
                        size="xs"
                        variant="ghost"
                        onClick={() => copyToClipboard(order.trackingNumber, "Tracking number")}
                      >
                        <FaCopy />
                      </Button>
                    </HStack>
                  )}
                  <HStack gap={2}>
                    <Text fontSize="sm" color="gray.600">Order Date:</Text>
                    <Text fontWeight="medium">{format(new Date(order.createdAt), 'PPP')}</Text>
                  </HStack>
                </VStack>
                <VStack align="end" gap={2}>
                  <Badge
                    colorScheme={getStatusColor(order.status)}
                    size="lg"
                    px={4}
                    py={2}
                    borderRadius="full"
                    fontSize="sm"
                  >
                    {getStatusLabel(order.status)}
                  </Badge>
                  <Text fontSize="xs" color="gray.500" textAlign="right" maxW="200px">
                    {getStatusDescription(order.status)}
                  </Text>
                </VStack>
              </HStack>

              {/* Status Alert */}
              {order.status === 'pending_payment' && (
                <Alert.Root status="warning" variant="subtle">
                  <Alert.Indicator />
                  <Alert.Title>Payment Required</Alert.Title>
                  <Alert.Description>
                    Please complete your payment to proceed with the order.
                    <Link color="blue.500" ml={1} textDecoration="underline">
                      Pay Now
                    </Link>
                  </Alert.Description>
                </Alert.Root>
              )}

              {order.status === 'shipped' && order.estimatedDelivery && (
                <Alert.Root status="info" variant="subtle">
                  <Alert.Indicator />
                  <Alert.Title>On the Way</Alert.Title>
                  <Alert.Description>
                    Estimated delivery: {format(new Date(order.estimatedDelivery), 'PPP')}
                  </Alert.Description>
                </Alert.Root>
              )}

              {order.status === 'delivered' && (
                <Alert.Root status="success" variant="subtle">
                  <Alert.Indicator />
                  <Alert.Title>Delivered Successfully</Alert.Title>
                  <Alert.Description>
                    Your order was delivered on {format(new Date(order.updatedAt), 'PPP')}
                  </Alert.Description>
                </Alert.Root>
              )}

              {/* Progress Bar */}
              <Box>
                <HStack justify="space-between" mb={3}>
                  <Text fontSize="sm" fontWeight="medium">Order Progress</Text>
                  <Text fontSize="sm" color="gray.600">{order.progress}% Complete</Text>
                </HStack>
                <Progress.Root value={order.progress} size="lg">
                  <Progress.Track bg="gray.200" borderRadius="full">
                    <Progress.Range bg={`${getStatusColor(order.status)}.500`} borderRadius="full" />
                  </Progress.Track>
                </Progress.Root>
                <HStack justify="space-between" mt={2} fontSize="xs" color="gray.500">
                  <Text>Order Placed</Text>
                  <Text>Payment</Text>
                  <Text>Confirmed</Text>
                  <Text>Shipped</Text>
                  <Text>Delivered</Text>
                </HStack>
              </Box>
            </VStack>
          </Card.Body>
        </Card.Root>

        <Grid templateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={8}>
          {/* Main Content */}
          <GridItem>
            <VStack gap={6} align="stretch">
              {/* Status Timeline */}
              <Card.Root>
                <Card.Header>
                  <HStack justify="space-between" align="center">
                    <Heading size="md">Order Timeline</Heading>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setShowFullTimeline(!showFullTimeline)}
                    >
                      {showFullTimeline ? 'Show Less' : 'Show All'}
                    </Button>
                  </HStack>
                </Card.Header>
                <Card.Body>
                  <VStack gap={6} align="stretch">
                    {order.timeline.map((item: any, index: number) => (
                      <Box key={item.key}>
                        <HStack gap={4} align="start">
                          <Box
                            w="48px"
                            h="48px"
                            borderRadius="full"
                            bg={item.isCompleted ? 'green.100' : item.isCurrent ? 'blue.100' : 'gray.100'}
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                            border="3px solid"
                            borderColor={item.isCompleted ? 'green.500' : item.isCurrent ? 'blue.500' : 'gray.300'}
                            position="relative"
                          >
                            {getStatusIcon(item.key, item.isCompleted, item.isCurrent)}
                            {item.isCompleted && (
                              <Box
                                position="absolute"
                                top="-2px"
                                right="-2px"
                                w="16px"
                                h="16px"
                                bg="green.500"
                                borderRadius="full"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                              >
                                <FaCheck size="8px" color="white" />
                              </Box>
                            )}
                          </Box>
                          <VStack align="start" flex="1" gap={2}>
                            <HStack justify="space-between" w="full">
                              <Text
                                fontWeight="bold"
                                fontSize="md"
                                color={item.isCompleted ? 'green.600' : item.isCurrent ? 'blue.600' : 'gray.500'}
                              >
                                {item.label}
                              </Text>
                              {item.timestamp && (
                                <Text fontSize="sm" color="gray.500" fontWeight="medium">
                                  {format(new Date(item.timestamp), 'MMM dd, yyyy HH:mm')}
                                </Text>
                              )}
                            </HStack>
                            {item.description && (
                              <Text fontSize="sm" color="gray.600" lineHeight="1.5">
                                {item.description}
                              </Text>
                            )}
                            {item.isCurrent && !item.isCompleted && (
                              <Badge colorScheme="blue" size="sm">
                                Current Status
                              </Badge>
                            )}
                          </VStack>
                        </HStack>
                        {index < order.timeline.length - 1 && (
                          <Box ml="24px" mt={3} mb={3}>
                            <Box
                              w="3px"
                              h="24px"
                              bg={item.isCompleted ? 'green.300' : 'gray.200'}
                              ml="21px"
                              borderRadius="full"
                            />
                          </Box>
                        )}
                      </Box>
                    ))}
                  </VStack>
                </Card.Body>
              </Card.Root>

              {/* Order Items */}
              <Card.Root>
                <Card.Header>
                  <HStack justify="space-between" align="center">
                    <Heading size="md">Order Items ({order.items.length})</Heading>
                    <Text fontSize="sm" color="gray.600">
                      Total: {formatPrice(Number(order.total), order.currency)}
                    </Text>
                  </HStack>
                </Card.Header>
                <Card.Body>
                  <VStack gap={6} align="stretch">
                    {order.items.map((item: any, index: number) => (
                      <Box key={item.id}>
                        <HStack gap={4} align="start">
                          <Box position="relative">
                            <Image
                              src={item.product.images?.[0]?.imageUrl || '/placeholder-product.jpg'}
                              alt={item.product.itemName}
                              w="100px"
                              h="100px"
                              objectFit="cover"
                              borderRadius="lg"
                              border="1px solid"
                              borderColor="gray.200"
                            />
                            {item.quantity > 1 && (
                              <Badge
                                position="absolute"
                                top="-8px"
                                right="-8px"
                                colorScheme="blue"
                                borderRadius="full"
                                fontSize="xs"
                              >
                                {item.quantity}x
                              </Badge>
                            )}
                          </Box>
                          <VStack align="start" flex="1" gap={2}>
                            <Text fontWeight="bold" fontSize="md" lineHeight="1.3">
                              {item.product.itemName}
                            </Text>
                            <HStack gap={4}>
                              <Text fontSize="sm" color="gray.600">
                                Qty: {item.quantity}
                              </Text>
                              <Text fontSize="sm" color="gray.600">
                                Type: {item.product.sellType === 'auction' ? 'Auction' : 'Buy Now'}
                              </Text>
                            </HStack>
                            <HStack justify="space-between" w="full">
                              <Text fontWeight="bold" color="blue.600" fontSize="lg">
                                {formatPrice(Number(item.price), item.currency || 'USD')}
                              </Text>
                              <Button size="sm" variant="outline">
                                View Product
                              </Button>
                            </HStack>
                          </VStack>
                        </HStack>
                        {index < order.items.length - 1 && <Separator mt={4} />}
                      </Box>
                    ))}
                  </VStack>
                </Card.Body>
              </Card.Root>
            </VStack>
          </GridItem>

          {/* Sidebar */}
          <GridItem>
            <VStack gap={6} align="stretch">
              {/* Order Summary */}
              <Card.Root>
                <Card.Header>
                  <HStack gap={2}>
                    <FaReceipt />
                    <Heading size="md">Order Summary</Heading>
                  </HStack>
                </Card.Header>
                <Card.Body>
                  <VStack gap={4} align="stretch">
                    <VStack gap={3} align="stretch">
                      <HStack justify="space-between">
                        <Text>Subtotal ({order.items.length} items):</Text>
                        <Text fontWeight="medium">
                          {formatPrice(Number(order.subtotal), order.currency)}
                        </Text>
                      </HStack>
                      <HStack justify="space-between">
                        <Text>Shipping Fee:</Text>
                        <Text fontWeight="medium" color={Number(order.shippingCost || 0) === 0 ? 'green.600' : 'inherit'}>
                          {Number(order.shippingCost || 0) === 0 ? 'FREE' : formatPrice(Number(order.shippingCost || 0), order.currency)}
                        </Text>
                      </HStack>
                      <HStack justify="space-between">
                        <Text>Tax & Fees:</Text>
                        <Text fontWeight="medium">
                          {formatPrice(Number(order.tax || 0), order.currency)}
                        </Text>
                      </HStack>
                      {order.discount && (
                        <HStack justify="space-between">
                          <Text color="green.600">Discount:</Text>
                          <Text fontWeight="medium" color="green.600">
                            -{formatPrice(Number(order.discount), order.currency)}
                          </Text>
                        </HStack>
                      )}
                    </VStack>
                    <Separator />
                    <HStack justify="space-between">
                      <Text fontSize="lg" fontWeight="bold">Total Paid:</Text>
                      <Text fontSize="lg" fontWeight="bold" color="blue.600">
                        {formatPrice(Number(order.total), order.currency)}
                      </Text>
                    </HStack>

                    {/* Payment Info */}
                    <Box bg="gray.50" p={3} borderRadius="md">
                      <VStack gap={2} align="stretch">
                        <HStack justify="space-between">
                          <Text fontSize="sm" color="gray.600">Payment Method:</Text>
                          <Text fontSize="sm" fontWeight="medium">
                            {order.paymentMethod?.replace('_', ' ').toUpperCase() || 'N/A'}
                          </Text>
                        </HStack>
                        <HStack justify="space-between">
                          <Text fontSize="sm" color="gray.600">Payment Status:</Text>
                          <Badge
                            colorScheme={order.paymentStatus === 'paid' ? 'green' : 'orange'}
                            size="sm"
                          >
                            {order.paymentStatus?.toUpperCase()}
                          </Badge>
                        </HStack>
                      </VStack>
                    </Box>
                  </VStack>
                </Card.Body>
              </Card.Root>

              {/* Shipping Address */}
              {order.shippingAddress && (
                <Card.Root>
                  <Card.Header>
                    <HStack gap={2}>
                      <FaMapMarkerAlt />
                      <Heading size="md">Delivery Address</Heading>
                    </HStack>
                  </Card.Header>
                  <Card.Body>
                    <VStack align="start" gap={3}>
                      <Box>
                        <Text fontWeight="bold" fontSize="md">{order.shippingAddress.name}</Text>
                        <Text fontSize="sm" color="gray.600" mt={1}>
                          {order.shippingAddress.address}
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                          {order.shippingAddress.city}, {order.shippingAddress.provinceRegion} {order.shippingAddress.zipCode}
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                          {order.shippingAddress.country}
                        </Text>
                      </Box>

                      {/* Contact Info */}
                      <Box w="full">
                        <Separator mb={3} />
                        <VStack gap={2} align="start">
                          <Text fontSize="sm" fontWeight="medium" color="gray.700">
                            Contact Information
                          </Text>
                          <HStack gap={2}>
                            <FaPhone size="12px" color="gray" />
                            <Text fontSize="sm">+****************</Text>
                          </HStack>
                          <HStack gap={2}>
                            <FaEnvelope size="12px" color="gray" />
                            <Text fontSize="sm"><EMAIL></Text>
                          </HStack>
                        </VStack>
                      </Box>
                    </VStack>
                  </Card.Body>
                </Card.Root>
              )}

              {/* Delivery Information */}
              {order.status === 'shipped' && (
                <Card.Root>
                  <Card.Header>
                    <HStack gap={2}>
                      <FaTruck />
                      <Heading size="md">Delivery Information</Heading>
                    </HStack>
                  </Card.Header>
                  <Card.Body>
                    <VStack gap={3} align="stretch">
                      {order.trackingNumber && (
                        <Box>
                          <Text fontSize="sm" fontWeight="medium" mb={2}>Tracking Details</Text>
                          <HStack justify="space-between" p={3} bg="blue.50" borderRadius="md">
                            <VStack align="start" gap={1}>
                              <Text fontSize="sm" color="gray.600">Tracking Number</Text>
                              <Text fontWeight="bold">{order.trackingNumber}</Text>
                            </VStack>
                            <Button size="sm" variant="outline">
                              Track Package
                            </Button>
                          </HStack>
                        </Box>
                      )}

                      {order.estimatedDelivery && (
                        <Box>
                          <Text fontSize="sm" fontWeight="medium" mb={2}>Estimated Delivery</Text>
                          <Text color="green.600" fontWeight="medium">
                            {format(new Date(order.estimatedDelivery), 'EEEE, MMMM dd, yyyy')}
                          </Text>
                          <Text fontSize="sm" color="gray.600">
                            Between 9:00 AM - 6:00 PM
                          </Text>
                        </Box>
                      )}
                    </VStack>
                  </Card.Body>
                </Card.Root>
              )}

              {/* Order Actions */}
              <Card.Root>
                <Card.Header>
                  <HStack gap={2}>
                    <FaHeadset />
                    <Heading size="md">Order Actions</Heading>
                  </HStack>
                </Card.Header>
                <Card.Body>
                  <VStack gap={3} align="stretch">
                    {/* Primary Actions */}
                    {order.status === 'pending_payment' && (
                      <Button colorScheme="blue" size="sm">
                        <FaCreditCard style={{ marginRight: '8px' }} />
                        Complete Payment
                      </Button>
                    )}

                    {order.status === 'delivered' && (
                      <Button colorScheme="green" variant="outline" size="sm">
                        <FaStar style={{ marginRight: '8px' }} />
                        Rate & Review
                      </Button>
                    )}

                    {/* Secondary Actions */}
                    <Button variant="outline" size="sm">
                      <FaDownload style={{ marginRight: '8px' }} />
                      Download Invoice
                    </Button>

                    <Button variant="outline" size="sm">
                      <FaEnvelope style={{ marginRight: '8px' }} />
                      Contact Seller
                    </Button>

                    <Separator />

                    {/* Support Actions */}
                    <Text fontSize="sm" fontWeight="medium" color="gray.700">
                      Need Help?
                    </Text>

                    <Button variant="ghost" size="sm" justifyContent="flex-start">
                      <FaHeadset style={{ marginRight: '8px' }} />
                      Live Chat Support
                    </Button>

                    <Button variant="ghost" size="sm" justifyContent="flex-start">
                      <FaPhone style={{ marginRight: '8px' }} />
                      Call Support: 1-800-HELP
                    </Button>

                    <Button variant="ghost" size="sm" justifyContent="flex-start">
                      <FaInfoCircle style={{ marginRight: '8px' }} />
                      Order FAQ
                    </Button>

                    {/* Conditional Actions */}
                    {(order.status === 'pending_payment' || order.status === 'paid') && (
                      <>
                        <Separator />
                        <Button
                          colorScheme="red"
                          variant="outline"
                          size="sm"
                          justifyContent="flex-start"
                        >
                          <FaUndo style={{ marginRight: '8px' }} />
                          Cancel Order
                        </Button>
                      </>
                    )}

                    {order.status === 'delivered' && (
                      <>
                        <Separator />
                        <Button
                          variant="outline"
                          size="sm"
                          justifyContent="flex-start"
                        >
                          <FaUndo style={{ marginRight: '8px' }} />
                          Return Item
                        </Button>
                      </>
                    )}
                  </VStack>
                </Card.Body>
              </Card.Root>

              {/* Security & Trust */}
              <Card.Root>
                <Card.Header>
                  <HStack gap={2}>
                    <FaShieldAlt />
                    <Heading size="md">Security & Protection</Heading>
                  </HStack>
                </Card.Header>
                <Card.Body>
                  <VStack gap={3} align="stretch">
                    <HStack gap={3}>
                      <FaShieldAlt color="green" />
                      <VStack align="start" gap={1}>
                        <Text fontSize="sm" fontWeight="medium">Buyer Protection</Text>
                        <Text fontSize="xs" color="gray.600">
                          Your purchase is protected by our guarantee
                        </Text>
                      </VStack>
                    </HStack>

                    <HStack gap={3}>
                      <FaCheck color="green" />
                      <VStack align="start" gap={1}>
                        <Text fontSize="sm" fontWeight="medium">Secure Payment</Text>
                        <Text fontSize="xs" color="gray.600">
                          Your payment information is encrypted and secure
                        </Text>
                      </VStack>
                    </HStack>

                    <HStack gap={3}>
                      <FaGift color="blue" />
                      <VStack align="start" gap={1}>
                        <Text fontSize="sm" fontWeight="medium">Return Policy</Text>
                        <Text fontSize="xs" color="gray.600">
                          30-day return policy for eligible items
                        </Text>
                      </VStack>
                    </HStack>
                  </VStack>
                </Card.Body>
              </Card.Root>
            </VStack>
          </GridItem>
        </Grid>
      </VStack>
    </Container>
  )
}

export default OrderTrackingPage
