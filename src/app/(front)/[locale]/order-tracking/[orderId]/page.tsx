'use client'
import React from 'react'
import { useParams } from 'next/navigation'
import {
  Box,
  Container,
  VStack,
  HStack,
  Text,
  Heading,
  Card,
  Badge,
  Grid,
  GridItem,
  Image,
  Button,

  Spinner,
  Icon,
  Separator
} from '@chakra-ui/react'
import {
  FaCheckCircle,
  FaClock,
  FaShippingFast,
  FaBox,
  FaMapMarkerAlt,
  FaCreditCard,
  FaPhone,
  FaEnvelope,
  FaCopy,
  FaTruck
} from 'react-icons/fa'

import { useOrderTrackingQuery } from '@/services/useOrderQuery'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'

const OrderTrackingPage = () => {
  const params = useParams()
  const orderId = params.orderId as string
  const { formatPrice } = useCurrencyLanguage()

  const { data: orderData, isLoading, error } = useOrderTrackingQuery(orderId)

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // You can add a toast notification here
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending_payment': return 'orange'
      case 'paid': return 'blue'
      case 'seller_confirmed': return 'purple'
      case 'shipped': return 'cyan'
      case 'delivered': return 'green'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  const getStatusIcon = (status: string, isCompleted: boolean, isCurrent: boolean) => {
    const iconProps = {
      size: '20px',
      color: isCompleted ? 'green.500' : isCurrent ? 'blue.500' : 'gray.400'
    }

    switch (status) {
      case 'pending_payment': return <FaCreditCard {...iconProps} />
      case 'paid': return <FaCheckCircle {...iconProps} />
      case 'seller_confirmed': return <FaBox {...iconProps} />
      case 'shipped': return <FaTruck {...iconProps} />
      case 'delivered': return <FaBox {...iconProps} />
      default: return <FaClock {...iconProps} />
    }
  }

  if (isLoading) {
    return (
      <Container maxW="6xl" py={8}>
        <VStack gap={8} align="center">
          <Spinner size="xl" />
          <Text>Loading order tracking...</Text>
        </VStack>
      </Container>
    )
  }

  if (error || !orderData?.order) {
    return (
      <Container maxW="6xl" py={8}>
        <Box p={4} bg="red.50" borderRadius="md" border="1px" borderColor="red.200">
          <Text fontWeight="bold" color="red.700">Order Not Found</Text>
          <Text fontSize="sm" color="red.600">
            The order you're looking for doesn't exist or you don't have permission to view it.
          </Text>
        </Box>
      </Container>
    )
  }

  const order = orderData.order

  return (
    <Container maxW="6xl" py={8}>
      <VStack gap={8} align="stretch">
        {/* Header */}
        <Card.Root>
          <Card.Body>
            <VStack gap={4} align="stretch">
              <HStack justify="space-between" align="start">
                <VStack align="start" gap={1}>
                  <Heading size="lg">Order Tracking</Heading>
                  <HStack gap={2}>
                    <Text fontSize="sm" color="gray.600">Invoice:</Text>
                    <Text fontWeight="bold">{order.orderNumber}</Text>
                    <Button
                      size="xs"
                      variant="ghost"
                      onClick={() => copyToClipboard(order.orderNumber)}
                    >
                      <FaCopy />
                    </Button>
                  </HStack>
                  {order.trackingNumber && (
                    <HStack gap={2}>
                      <Text fontSize="sm" color="gray.600">Tracking:</Text>
                      <Text fontWeight="bold">{order.trackingNumber}</Text>
                      <Button
                        size="xs"
                        variant="ghost"
                        onClick={() => copyToClipboard(order.trackingNumber)}
                      >
                        <FaCopy />
                      </Button>
                    </HStack>
                  )}
                </VStack>
                <Badge
                  colorScheme={getStatusColor(order.status)}
                  size="lg"
                  px={3}
                  py={1}
                  borderRadius="full"
                >
                  {order.status.replace('_', ' ').toUpperCase()}
                </Badge>
              </HStack>

              {/* Progress Bar */}
              <Box>
                <HStack justify="space-between" mb={2}>
                  <Text fontSize="sm" fontWeight="medium">Order Progress</Text>
                  <Text fontSize="sm" color="gray.600">{order.progress}% Complete</Text>
                </HStack>
                <Box w="full" bg="gray.200" borderRadius="full" h="4">
                  <Box
                    bg={`${getStatusColor(order.status)}.500`}
                    h="4"
                    borderRadius="full"
                    w={`${order.progress}%`}
                    transition="width 0.3s ease"
                  />
                </Box>
              </Box>
            </VStack>
          </Card.Body>
        </Card.Root>

        <Grid templateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={8}>
          {/* Main Content */}
          <GridItem>
            <VStack gap={6} align="stretch">
              {/* Status Timeline */}
              <Card.Root>
                <Card.Header>
                  <Heading size="md">Order Timeline</Heading>
                </Card.Header>
                <Card.Body>
                  <VStack gap={4} align="stretch">
                    {order.timeline.map((item: any, index: number) => (
                      <Box key={item.key}>
                        <HStack gap={4} align="start">
                          <Box
                            w="40px"
                            h="40px"
                            borderRadius="full"
                            bg={item.isCompleted ? 'green.100' : item.isCurrent ? 'blue.100' : 'gray.100'}
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                            border="2px solid"
                            borderColor={item.isCompleted ? 'green.500' : item.isCurrent ? 'blue.500' : 'gray.300'}
                          >
                            {getStatusIcon(item.key, item.isCompleted, item.isCurrent)}
                          </Box>
                          <VStack align="start" flex="1" gap={1}>
                            <Text
                              fontWeight="bold"
                              color={item.isCompleted ? 'green.600' : item.isCurrent ? 'blue.600' : 'gray.500'}
                            >
                              {item.label}
                            </Text>
                            {item.description && (
                              <Text fontSize="sm" color="gray.600">
                                {item.description}
                              </Text>
                            )}
                            {item.timestamp && (
                              <Text fontSize="xs" color="gray.500">
                                {new Date(item.timestamp).toLocaleString()}
                              </Text>
                            )}
                          </VStack>
                        </HStack>
                        {index < order.timeline.length - 1 && (
                          <Box ml="20px" mt={2} mb={2}>
                            <Box
                              w="2px"
                              h="20px"
                              bg={item.isCompleted ? 'green.300' : 'gray.200'}
                              ml="18px"
                            />
                          </Box>
                        )}
                      </Box>
                    ))}
                  </VStack>
                </Card.Body>
              </Card.Root>

              {/* Order Items */}
              <Card.Root>
                <Card.Header>
                  <Heading size="md">Order Items</Heading>
                </Card.Header>
                <Card.Body>
                  <VStack gap={4} align="stretch">
                    {order.items.map((item: any) => (
                      <Box key={item.id}>
                        <HStack gap={4} align="start">
                          <Image
                            src={item.product.images?.[0]?.url || '/placeholder-product.jpg'}
                            alt={item.product.itemName}
                            w="80px"
                            h="80px"
                            objectFit="cover"
                            borderRadius="md"
                          />
                          <VStack align="start" flex="1" gap={1}>
                            <Text fontWeight="bold">{item.product.itemName}</Text>
                            <Text fontSize="sm" color="gray.600">
                              Quantity: {item.quantity}
                            </Text>
                            <Text fontWeight="bold" color="blue.600">
                              {formatPrice(Number(item.price), item.currency)}
                            </Text>
                          </VStack>
                        </HStack>
                        <Separator mt={4} />
                      </Box>
                    ))}
                  </VStack>
                </Card.Body>
              </Card.Root>
            </VStack>
          </GridItem>

          {/* Sidebar */}
          <GridItem>
            <VStack gap={6} align="stretch">
              {/* Order Summary */}
              <Card.Root>
                <Card.Header>
                  <Heading size="md">Order Summary</Heading>
                </Card.Header>
                <Card.Body>
                  <VStack gap={3} align="stretch">
                    <HStack justify="space-between">
                      <Text>Subtotal:</Text>
                      <Text fontWeight="bold">
                        {formatPrice(Number(order.subtotal), order.currency)}
                      </Text>
                    </HStack>
                    <HStack justify="space-between">
                      <Text>Shipping:</Text>
                      <Text fontWeight="bold">
                        {formatPrice(Number(order.shippingCost || 0), order.currency)}
                      </Text>
                    </HStack>
                    <HStack justify="space-between">
                      <Text>Tax:</Text>
                      <Text fontWeight="bold">
                        {formatPrice(Number(order.tax || 0), order.currency)}
                      </Text>
                    </HStack>
                    <Separator />
                    <HStack justify="space-between">
                      <Text fontSize="lg" fontWeight="bold">Total:</Text>
                      <Text fontSize="lg" fontWeight="bold" color="blue.600">
                        {formatPrice(Number(order.total), order.currency)}
                      </Text>
                    </HStack>
                  </VStack>
                </Card.Body>
              </Card.Root>

              {/* Shipping Address */}
              {order.shippingAddress && (
                <Card.Root>
                  <Card.Header>
                    <HStack gap={2}>
                      <FaMapMarkerAlt />
                      <Heading size="md">Shipping Address</Heading>
                    </HStack>
                  </Card.Header>
                  <Card.Body>
                    <VStack align="start" gap={1}>
                      <Text fontWeight="bold">{order.shippingAddress.fullName}</Text>
                      <Text>{order.shippingAddress.address}</Text>
                      <Text>
                        {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
                      </Text>
                      <Text>{order.shippingAddress.country}</Text>
                      {order.shippingAddress.phone && (
                        <HStack gap={2}>
                          <FaPhone size="12px" />
                          <Text fontSize="sm">{order.shippingAddress.phone}</Text>
                        </HStack>
                      )}
                    </VStack>
                  </Card.Body>
                </Card.Root>
              )}

              {/* Contact Support */}
              <Card.Root>
                <Card.Header>
                  <Heading size="md">Need Help?</Heading>
                </Card.Header>
                <Card.Body>
                  <VStack gap={3} align="stretch">
                    <Button variant="outline" size="sm">
                      <FaEnvelope style={{ marginRight: '8px' }} />
                      Contact Support
                    </Button>
                    <Button variant="outline" size="sm">
                      <FaPhone style={{ marginRight: '8px' }} />
                      Call Us
                    </Button>
                    {order.status === 'pending_payment' && (
                      <Button colorScheme="red" variant="outline" size="sm">
                        Cancel Order
                      </Button>
                    )}
                  </VStack>
                </Card.Body>
              </Card.Root>
            </VStack>
          </GridItem>
        </Grid>
      </VStack>
    </Container>
  )
}

export default OrderTrackingPage
