import "@fontsource/poppins";
import type { Metadata } from "next";
import { Provider } from "@/components/ui/provider"
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';
import AppFrontLayout from '@/components/layouts/front/AppFrontLayout';
import React from 'react'
import { hasLocale, NextIntlClientProvider } from "next-intl";
import { ReactQueryClientProvider } from "@/components/ReactQueryClientProvider";
import { CurrencyLanguageProvider } from "@/contexts/CurrencyLanguageContext";

export const metadata: Metadata = {
  title: "King Collectble",
  description: "",
};

export default async function layout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  return (
    <html lang={locale} suppressHydrationWarning>
      <body>
        <NextIntlClientProvider>
          <Provider>
            <ReactQueryClientProvider>
              <CurrencyLanguageProvider>
                <AppFrontLayout>
                  {children}
                </AppFrontLayout>
              </CurrencyLanguageProvider>
            </ReactQueryClientProvider>
          </Provider>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
