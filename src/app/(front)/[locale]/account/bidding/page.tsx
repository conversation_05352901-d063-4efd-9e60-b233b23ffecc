'use client'
import React, { useState } from 'react'
import {
    Box,
    Button,
    Heading,
    Text,
    VStack,
    HStack,
    Image,
    Badge,
    Grid,
    Skeleton,
    Flex,
    Icon,
    Container,
    Separator,
} from '@chakra-ui/react'
import {
    FaEye,
    FaGavel,
    FaClock,
    FaDollarSign,
    FaSearch,
    FaTrophy,
    FaTimesCircle,
    FaCheckCircle,
    FaSpinner
} from 'react-icons/fa'
import { useUserBidsQuery, UserBidsQueryParams } from '@/services/useBiddingQuery'
import { formatUSD } from '@/utils/helpers/helper'
import { formatDistanceToNow, format } from 'date-fns'
import { useRouter } from 'next/navigation'
import FormSelectField, { SelectOption } from '@/components/ui/form/FormSelectField'
import { SingleValue } from 'react-select'
import { useTranslations } from 'next-intl'
import { useSession } from 'next-auth/react'
import Link from 'next/link'

// Status options
const statusOptions: SelectOption[] = [
    { value: 'all', label: 'All Status' },
    { value: 'active', label: 'Active Auctions' },
    { value: 'ended', label: 'Ended Auctions' },
    { value: 'won', label: 'Won Auctions' },
    { value: 'lost', label: 'Lost Auctions' },
]

// Auction status badge component
const AuctionStatusBadge = ({ status }: { status: string }) => {
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active': return 'blue'
            case 'ended': return 'gray'
            case 'won': return 'green'
            case 'lost': return 'red'
            default: return 'gray'
        }
    }

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'active': return FaSpinner
            case 'ended': return FaClock
            case 'won': return FaTrophy
            case 'lost': return FaTimesCircle
            default: return FaClock
        }
    }

    return (
        <Badge colorScheme={getStatusColor(status)} variant="subtle">
            <HStack gap={1}>
                <Icon as={getStatusIcon(status)} boxSize={3} />
                <Text textTransform="capitalize">{status}</Text>
            </HStack>
        </Badge>
    )
}

// Winning status badge component
const WinningStatusBadge = ({ isWinning, auctionStatus }: { isWinning: boolean; auctionStatus: string }) => {
    if (auctionStatus === 'active') {
        return (
            <Badge colorScheme={isWinning ? 'green' : 'orange'} variant="outline" size="sm">
                <Text>{isWinning ? 'Winning' : 'Outbid'}</Text>
            </Badge>
        )
    }
    return null
}

const AccountBiddingPage = () => {
    const router = useRouter()
    const t = useTranslations()
    const { data: session } = useSession()
    const [statusFilter, setStatusFilter] = useState<string>('all')
    const [currentPage, setCurrentPage] = useState(1)

    // Query parameters for user's bids
    const queryParams: UserBidsQueryParams = {
        page: currentPage,
        limit: 10,
        status: statusFilter !== 'all' ? statusFilter as any : undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc',
    }

    const { data: bidsData, isLoading, error } = useUserBidsQuery(queryParams)

    const handleStatusChange = (option: SingleValue<SelectOption>) => {
        setStatusFilter(option?.value || 'all')
        setCurrentPage(1)
    }

    const handleViewBid = (productId: string, productSlug?: string) => {
        if (productSlug) {
            router.push(`/auction/${productSlug}`)
        } else {
            router.push(`/auction/product/${productId}`)
        }
    }

    if (!session) {
        return (
            <Container maxW="6xl" py={8}>
                <Text>Please login to view your bidding history.</Text>
            </Container>
        )
    }

    return (
        <Container maxW="6xl" py={8}>
            {/* Header */}
            <VStack align="start" gap={6} mb={8}>
                <VStack align="start" gap={1}>
                    <Heading size="xl">My Bidding History</Heading>
                    <Text color="gray.600">
                        Track your auction bids and results
                    </Text>
                </VStack>

                {/* Filters */}
                <Grid templateColumns={{ base: '1fr', md: '1fr 2fr' }} gap={4} w="full">
                    <FormSelectField
                        label="Auction Status"
                        options={statusOptions}
                        value={statusOptions.find(opt => opt.value === statusFilter)}
                        onChange={(selectedOption) => {
                            const option = selectedOption as SingleValue<SelectOption>;
                            handleStatusChange(option)
                        }}
                        placeholder="Select status..."
                    />
                </Grid>
            </VStack>

            {/* Bidding List */}
            {isLoading ? (
                <VStack gap={4} align="stretch">
                    {Array.from({ length: 5 }).map((_, index) => (
                        <Skeleton key={index} height="200px" />
                    ))}
                </VStack>
            ) : error ? (
                <Box p={8} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
                    <VStack gap={4}>
                        <Icon as={FaTimesCircle} boxSize={12} color="red.500" />
                        <Text color="red.500">Failed to load bidding history</Text>
                    </VStack>
                </Box>
            ) : !bidsData?.bids?.length ? (
                <Box p={8} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
                    <VStack gap={4}>
                        <Icon as={FaGavel} boxSize={12} color="gray.400" />
                        <Text color="gray.500">No bidding history found</Text>
                        <Button colorScheme="blue" onClick={() => router.push('/marketplace?sellType=auction')}>
                            Browse Auctions
                        </Button>
                    </VStack>
                </Box>
            ) : (
                <VStack gap={6} align="stretch">
                    {bidsData?.bids?.map((bid) => (
                        <Box key={bid.productId} p={6} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
                            <VStack gap={4} align="stretch">
                                {/* Bid Header */}
                                <Flex justify="space-between" align="start" wrap="wrap" gap={4}>
                                    <VStack align="start" gap={1}>
                                        <Text fontWeight="bold" fontSize="lg">
                                            {bid.product.itemName}
                                        </Text>
                                        <Text color="gray.600" fontSize="sm">
                                            Last bid {formatDistanceToNow(new Date(bid.lastBidTime), { addSuffix: true })}
                                        </Text>
                                    </VStack>
                                    <HStack gap={2}>
                                        <AuctionStatusBadge status={bid.auctionStatus} />
                                        <WinningStatusBadge isWinning={bid.isWinning} auctionStatus={bid.auctionStatus} />
                                    </HStack>
                                </Flex>

                                <Separator />

                                {/* Bid Content */}
                                <HStack gap={6} align="start">
                                    <Image
                                        src={bid.product.images.find((img: any) => img.isMain)?.imageUrl || '/placeholder.jpg'}
                                        alt={bid.product.itemName}
                                        boxSize="120px"
                                        objectFit="cover"
                                        borderRadius="md"
                                    />
                                    <VStack align="start" flex={1} gap={3}>
                                        <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} gap={4} w="full">
                                            <VStack align="start" gap={2}>
                                                <Text fontSize="sm" color="gray.600">Your Highest Bid</Text>
                                                <Text fontSize="xl" fontWeight="bold" color="gray.800">
                                                    {formatUSD(bid.highestBid)}
                                                </Text>
                                            </VStack>
                                            <VStack align="start" gap={2}>
                                                <Text fontSize="sm" color="gray.600">Current Bid</Text>
                                                <Text fontSize="xl" fontWeight="bold">
                                                    {formatUSD(bid.product.currentBid || 0)}
                                                </Text>
                                            </VStack>
                                        </Grid>

                                        <HStack gap={4} wrap="wrap">
                                            <Text fontSize="sm" color="gray.600">
                                                Total Bids: {bid.totalBids}
                                            </Text>
                                            {bid.product.auctionEndDate && (
                                                <Text fontSize="sm" color="gray.600">
                                                    {bid.auctionStatus === 'active' ? 'Ends' : 'Ended'}: {' '}
                                                    {format(new Date(bid.product.auctionEndDate), 'MMM dd, yyyy HH:mm')}
                                                </Text>
                                            )}
                                        </HStack>
                                    </VStack>
                                </HStack>

                                <Separator />

                                {/* Actions */}
                                <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
                                    <VStack align="start" gap={1}>
                                        <Text fontSize="sm" color="gray.600">
                                            {bid.auctionStatus === 'won' && 'Congratulations! You won this auction'}
                                            {bid.auctionStatus === 'lost' && 'Auction ended - You did not win'}
                                            {bid.auctionStatus === 'active' && bid.isWinning && 'You are currently winning!'}
                                            {bid.auctionStatus === 'active' && !bid.isWinning && 'You have been outbid'}
                                            {bid.auctionStatus === 'ended' && 'Auction has ended'}
                                        </Text>
                                    </VStack>
                                    <Button
                                        colorScheme="blue"
                                        variant="outline"
                                        onClick={() => handleViewBid(bid.productId, bid.product.slug)}
                                    >
                                        <HStack gap={2}>
                                            <Icon as={FaEye} />
                                            <Text>View Auction</Text>
                                        </HStack>
                                    </Button>
                                </Flex>
                            </VStack>
                        </Box>
                    ))}

                    {/* Pagination */}
                    {bidsData?.pagination && bidsData.pagination.totalPages > 1 && (
                        <Flex justify="center" gap={2} mt={6}>
                            <Button
                                variant="outline"
                                disabled={currentPage === 1}
                                onClick={() => setCurrentPage(currentPage - 1)}
                            >
                                Previous
                            </Button>
                            <Text alignSelf="center" mx={4}>
                                Page {currentPage} of {bidsData.pagination.totalPages}
                            </Text>
                            <Button
                                variant="outline"
                                disabled={currentPage === bidsData.pagination.totalPages}
                                onClick={() => setCurrentPage(currentPage + 1)}
                            >
                                Next
                            </Button>
                        </Flex>
                    )}
                </VStack>
            )}
        </Container>
    )
}

export default AccountBiddingPage
