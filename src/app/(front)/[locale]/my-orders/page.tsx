'use client'
import React, { useState } from 'react'
import {
  Box,
  Container,
  VStack,
  HStack,
  Text,
  Heading,
  Card,
  Badge,
  Grid,
  GridItem,
  Image,
  Button,
  Input,
  Spinner,
  Alert
} from '@chakra-ui/react'

import {
  FaSearch,
  FaEye,
  FaShippingFast,
  FaBox,
  FaCheckCircle,
  FaTimes
} from 'react-icons/fa'
import { useUserOrdersQuery } from '@/services/useOrderQuery'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import Link from 'next/link'

const MyOrdersPage = () => {
  const { formatPrice } = useCurrencyLanguage()
  const [currentTab, setCurrentTab] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<'createdAt' | 'total' | 'status' | 'orderNumber'>('createdAt')
  const [page, setPage] = useState(1)

  const getStatusFilter = (tab: string) => {
    switch (tab) {
      case 'pending': return 'pending'
      case 'paid': return 'processing' // Map paid to processing since that's the closest match
      case 'shipped': return 'shipped'
      case 'delivered': return 'delivered'
      case 'cancelled': return 'cancelled'
      default: return undefined
    }
  }

  const { data: ordersData, isLoading, error } = useUserOrdersQuery({
    page,
    limit: 10,
    status: getStatusFilter(currentTab),
    sortBy,
    sortOrder: 'desc'
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending_payment': return 'orange'
      case 'paid': return 'blue'
      case 'seller_confirmed': return 'purple'
      case 'shipped': return 'cyan'
      case 'delivered': return 'green'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending_payment': return <FaBox />
      case 'paid': return <FaCheckCircle />
      case 'shipped': return <FaShippingFast />
      case 'delivered': return <FaCheckCircle />
      case 'cancelled': return <FaTimes />
      default: return <FaBox />
    }
  }

  const filteredOrders = ordersData?.orders?.filter((order: any) =>
    order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
    order.items.some((item: any) => 
      item.product.itemName.toLowerCase().includes(searchQuery.toLowerCase())
    )
  ) || []

  if (isLoading) {
    return (
      <Container maxW="6xl" py={8}>
        <VStack gap={8} align="center">
          <Spinner size="xl" />
          <Text>Loading your orders...</Text>
        </VStack>
      </Container>
    )
  }

  if (error) {
    return (
      <Container maxW="6xl" py={8}>
        <Box p={4} bg="red.50" borderRadius="md" border="1px" borderColor="red.200">
          <Text color="red.700" fontWeight="medium">Error Loading Orders</Text>
          <Text fontSize="sm" color="red.600">
            Failed to load your orders. Please try again later.
          </Text>
        </Box>
      </Container>
    )
  }

  return (
    <Container maxW="6xl" py={8}>
      <VStack gap={8} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="xl" mb={2}>My Orders</Heading>
          <Text color="gray.600">Track and manage your orders</Text>
        </Box>

        {/* Search and Filters */}
        <Card.Root>
          <Card.Body>
            <HStack gap={4} wrap="wrap">
              <Box flex="1" minW="300px">
                <Input
                  placeholder="Search by order number or product name..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Box>
              <Box w="200px">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'createdAt' | 'total' | 'status' | 'orderNumber')}
                  style={{ width: '100%', padding: '8px', borderRadius: '6px', border: '1px solid #e2e8f0' }}
                >
                  <option value="createdAt">Sort by Date</option>
                  <option value="total">Sort by Amount</option>
                  <option value="status">Sort by Status</option>
                </select>
              </Box>
            </HStack>
          </Card.Body>
        </Card.Root>

        {/* Simple Tab Navigation */}
        <Box>
          <HStack gap={4} mb={6} wrap="wrap">
            {['all', 'pending', 'paid', 'shipped', 'delivered', 'cancelled'].map((tab) => (
              <Button
                key={tab}
                variant={currentTab === tab ? 'solid' : 'outline'}
                colorScheme={currentTab === tab ? 'blue' : 'gray'}
                size="sm"
                onClick={() => setCurrentTab(tab)}
                textTransform="capitalize"
              >
                {tab === 'all' ? 'All Orders' : tab}
              </Button>
            ))}
          </HStack>

          <OrdersList orders={filteredOrders} formatPrice={(amount, currency) => formatPrice(amount, currency as any)} />
        </Box>
      </VStack>
    </Container>
  )
}

interface OrdersListProps {
  orders: any[]
  formatPrice: (amount: number, currency: string) => string
}

const OrdersList: React.FC<OrdersListProps> = ({ orders, formatPrice }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending_payment': return 'orange'
      case 'paid': return 'blue'
      case 'seller_confirmed': return 'purple'
      case 'shipped': return 'cyan'
      case 'delivered': return 'green'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  if (orders.length === 0) {
    return (
      <Box py={12}>
        <Box textAlign="center" py={8}>
          <Text fontSize="lg" fontWeight="medium" color="gray.600">
            No orders found
          </Text>
          <Text fontSize="sm" color="gray.500" mt={2}>
            You haven't placed any orders yet or no orders match your search criteria.
          </Text>
        </Box>
      </Box>
    )
  }

  return (
    <VStack gap={4} align="stretch">
      {orders.map((order: any) => (
        <Card.Root key={order.id} _hover={{ shadow: 'lg' }} transition="all 0.2s">
          <Card.Body>
            <Grid templateColumns={{ base: '1fr', md: '1fr 200px' }} gap={6}>
              <GridItem>
                <VStack align="stretch" gap={4}>
                  {/* Order Header */}
                  <HStack justify="space-between" align="start">
                    <VStack align="start" gap={1}>
                      <HStack gap={2}>
                        <Text fontWeight="bold" fontSize="lg">
                          {order.orderNumber}
                        </Text>
                        <Badge
                          colorScheme={getStatusColor(order.status)}
                          px={2}
                          py={1}
                          borderRadius="full"
                        >
                          {order.status.replace('_', ' ').toUpperCase()}
                        </Badge>
                      </HStack>
                      <Text fontSize="sm" color="gray.600">
                        Ordered on {new Date(order.createdAt).toLocaleDateString()}
                      </Text>
                    </VStack>
                    <Text fontWeight="bold" fontSize="lg" color="blue.600">
                      {formatPrice(Number(order.total), order.currency)}
                    </Text>
                  </HStack>

                  {/* Progress Bar */}
                  <Box>
                    <HStack justify="space-between" mb={2}>
                      <Text fontSize="sm" fontWeight="medium">Progress</Text>
                      <Text fontSize="sm" color="gray.600">{order.progress}%</Text>
                    </HStack>
                    <Box w="full" bg="gray.200" borderRadius="full" h="2">
                      <Box
                        bg={`${getStatusColor(order.status)}.500`}
                        h="2"
                        borderRadius="full"
                        w={`${order.progress}%`}
                        transition="width 0.3s ease"
                      />
                    </Box>
                  </Box>

                  {/* Order Items Preview */}
                  <Box>
                    <Text fontSize="sm" fontWeight="medium" mb={2}>Items:</Text>
                    <HStack gap={3} wrap="wrap">
                      {order.items.slice(0, 3).map((item: any) => (
                        <HStack key={item.id} gap={2}>
                          <Image
                            src={item.product.images?.[0]?.url || '/placeholder-product.jpg'}
                            alt={item.product.itemName}
                            w="40px"
                            h="40px"
                            objectFit="cover"
                            borderRadius="md"
                          />
                          <VStack align="start" gap={0}>
                            <Text fontSize="sm" fontWeight="medium" lineClamp={1}>
                              {item.product.itemName}
                            </Text>
                            <Text fontSize="xs" color="gray.600">
                              Qty: {item.quantity}
                            </Text>
                          </VStack>
                        </HStack>
                      ))}
                      {order.items.length > 3 && (
                        <Text fontSize="sm" color="gray.600">
                          +{order.items.length - 3} more items
                        </Text>
                      )}
                    </HStack>
                  </Box>
                </VStack>
              </GridItem>

              <GridItem>
                <VStack gap={3} align="stretch">
                  <Link href={`/order-tracking/${order.id}`} passHref>
                    <Button
                      colorScheme="blue"
                      variant="outline"
                      size="sm"
                      w="full"
                    >
                      <FaEye style={{ marginRight: '8px' }} />
                      Track Order
                    </Button>
                  </Link>
                  
                  {order.status === 'pending_payment' && (
                    <Button
                      colorScheme="green"
                      size="sm"
                      w="full"
                    >
                      Pay Now
                    </Button>
                  )}
                  
                  {order.status === 'delivered' && (
                    <Button
                      colorScheme="orange"
                      variant="outline"
                      size="sm"
                      w="full"
                    >
                      Leave Review
                    </Button>
                  )}
                  
                  {['pending_payment', 'paid'].includes(order.status) && (
                    <Button
                      colorScheme="red"
                      variant="ghost"
                      size="sm"
                      w="full"
                    >
                      Cancel Order
                    </Button>
                  )}
                </VStack>
              </GridItem>
            </Grid>
          </Card.Body>
        </Card.Root>
      ))}
    </VStack>
  )
}

export default MyOrdersPage
