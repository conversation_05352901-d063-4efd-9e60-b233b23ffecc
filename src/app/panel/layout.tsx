import "@fontsource/poppins";

import type { Metada<PERSON> } from "next";
import { Provider } from "@/components/ui/provider"
import { ReactQueryClientProvider } from "@/components/ReactQueryClientProvider";
import { AuthProvider } from "@/components/auth/AuthProvider";

export const metadata: Metadata = {
  title: "Dashboard King Collectble",
  description: "",
};

export default function RootLayout(props: { children: React.ReactNode }) {
  const { children } = props
  return (
    <html suppressHydrationWarning>
      <body>
        <Provider>
          <AuthProvider>
            <ReactQueryClientProvider>
              {children}
            </ReactQueryClientProvider>
          </AuthProvider>
        </Provider>
      </body>
    </html>
  )
}