'use client';

import { useAuth } from '@/hooks/useAuth';
import {
  <PERSON>,
  But<PERSON>,
  Card,
  Heading,
  HStack,
  Text,
  VStack,
  Avatar,
  Badge,
  Spinner,
  Center,
} from '@chakra-ui/react';

export default function DashboardPage() {

  // if (isLoading) {
  //   return (
  //     <Center h="100vh">
  //       <VStack>
  //         <Spinner size="xl" color="blue.500" />
  //         <Text>Loading dashboard...</Text>
  //       </VStack>
  //     </Center>
  //   );
  // }

  return (
    <Box p={8} maxW="6xl" mx="auto">
      <VStack align="stretch" gap={8}>
        {/* Header */}
        {/* <HStack justify="space-between" align="center">
          <VStack align="start" gap={1}>
            <Heading size="2xl" color="gray.800">
              Dashboard
            </Heading>
            <Text color="gray.600">
              Welcome back
            </Text>
          </VStack>
          <Button
            colorScheme="red"
            variant="outline"
            onClick={logout}
            size="sm"
          >
            Logout
          </Button>
        </HStack> */}

        {/* User Profile Card */}
        {/* <Card.Root>
          <Card.Header>
            <HStack gap={4}>
              <VStack align="start" gap={1}>
                <Heading size="lg">
                  {user?.firstName} {user?.lastName}
                </Heading>
                <Text color="gray.600">{user?.email}</Text>
                <Text color="gray.600">{user?.phoneNumber}</Text>
                <Badge colorScheme="green" size="sm">
                  Active
                </Badge>
              </VStack>
            </HStack>
          </Card.Header>
          <Card.Body>
            <HStack gap={4}>
              <Button
                colorScheme="gray"
                variant="outline"
                size="sm"
              >
                Edit Profile
              </Button>
            </HStack>
          </Card.Body>
        </Card.Root> */}

        {/* Quick Actions */}
        <Card.Root>
          <Card.Header>
            <Heading size="lg">Quick Actions</Heading>
          </Card.Header>
          <Card.Body>
            <VStack align="stretch" gap={4}>
              <HStack gap={4} wrap="wrap">
                <Button colorScheme="blue" size="lg">
                  View Collections
                </Button>
                <Button colorScheme="green" size="lg">
                  Add New Item
                </Button>
                <Button colorScheme="purple" size="lg">
                  Manage Auctions
                </Button>
                <Button colorScheme="orange" size="lg">
                  View Analytics
                </Button>
              </HStack>
            </VStack>
          </Card.Body>
        </Card.Root>

        {/* Stats Cards */}
        <HStack gap={6} wrap="wrap">
          <Card.Root flex="1" minW="200px">
            <Card.Body textAlign="center">
              <VStack gap={2}>
                <Text fontSize="3xl" fontWeight="bold" color="blue.500">
                  12
                </Text>
                <Text color="gray.600">Total Collections</Text>
              </VStack>
            </Card.Body>
          </Card.Root>

          <Card.Root flex="1" minW="200px">
            <Card.Body textAlign="center">
              <VStack gap={2}>
                <Text fontSize="3xl" fontWeight="bold" color="green.500">
                  5
                </Text>
                <Text color="gray.600">Active Auctions</Text>
              </VStack>
            </Card.Body>
          </Card.Root>

          <Card.Root flex="1" minW="200px">
            <Card.Body textAlign="center">
              <VStack gap={2}>
                <Text fontSize="3xl" fontWeight="bold" color="purple.500">
                  $2,450
                </Text>
                <Text color="gray.600">Total Earnings</Text>
              </VStack>
            </Card.Body>
          </Card.Root>

          <Card.Root flex="1" minW="200px">
            <Card.Body textAlign="center">
              <VStack gap={2}>
                <Text fontSize="3xl" fontWeight="bold" color="orange.500">
                  28
                </Text>
                <Text color="gray.600">Completed Sales</Text>
              </VStack>
            </Card.Body>
          </Card.Root>
        </HStack>

        {/* Recent Activity */}
        <Card.Root>
          <Card.Header>
            <Heading size="lg">Recent Activity</Heading>
          </Card.Header>
          <Card.Body>
            <VStack align="stretch" gap={3}>
              <HStack justify="space-between" p={3} bg="gray.50" borderRadius="md">
                <Text>New bid on "Vintage Star Wars Figure"</Text>
                <Text fontSize="sm" color="gray.600">2 hours ago</Text>
              </HStack>
              <HStack justify="space-between" p={3} bg="gray.50" borderRadius="md">
                <Text>Collection "Marvel Comics Set" was sold</Text>
                <Text fontSize="sm" color="gray.600">1 day ago</Text>
              </HStack>
              <HStack justify="space-between" p={3} bg="gray.50" borderRadius="md">
                <Text>New item added to "Pokemon Cards"</Text>
                <Text fontSize="sm" color="gray.600">3 days ago</Text>
              </HStack>
            </VStack>
          </Card.Body>
        </Card.Root>
      </VStack>
    </Box>
  );
}
