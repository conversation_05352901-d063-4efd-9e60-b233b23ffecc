{"name": "Colombia", "iso3": "COL", "iso2": "CO", "phone_code": "57", "capital": "Bogotá", "currency": "COP", "currency_name": "Colombian peso", "currency_symbol": "$", "emoji": "🇨🇴", "emojiU": "U+1F1E8 U+1F1F4", "translations": {"kr": "콜롬비아", "br": "Colômbia", "pt": "Colômbia", "nl": "Colombia", "hr": "Kolumbija", "fa": "کلمبیا", "de": "Kolumbien", "es": "Colombia", "fr": "<PERSON><PERSON><PERSON>", "ja": "コロンビア", "it": "Colombia", "cn": "哥伦比亚", "tr": "Kolombiya"}, "states": [{"id": 81, "name": "Amazonas", "country_id": 1, "department_code": null, "state_code": "AMA", "cities": ["La chorrera", "<PERSON><PERSON><PERSON>", "Puerto nariño"]}, {"id": 13, "name": "Antioquia", "country_id": 1, "department_code": null, "state_code": "ANT", "cities": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Alejandria", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Andes", "Angelopolis", "Angostura", "<PERSON><PERSON>", "<PERSON><PERSON>", "Apartado", "Arboletes", "Argelia (a)", "Armenia", "<PERSON><PERSON><PERSON>", "Bello", "Belmira", "Betania", "<PERSON>ulia (ant)", "<PERSON><PERSON><PERSON>", "Buritica", "Caceres", "Caicedo", "Caldas", "Campamento", "Cañasgordas", "Caracoli", "Caramanta", "Carepa", "Carolina", "Casabe - yondo", "Caucasia", "Chigorod<PERSON>", "Cisneros", "Ciudad bolivar", "<PERSON><PERSON><PERSON>", "Concepcion", "Concordia", "Copacabana", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Dabeiba", "<PERSON>", "<PERSON><PERSON>", "Ebejico", "El bagre", "El carmen de viboral", "El santuario", "El valle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Fredonia", "Frontino", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON> plata", "Granada", "Guadalupe", "<PERSON><PERSON><PERSON>", "Guatape", "Heliconia", "Hispania", "Itagu<PERSON>", "Ituango", "Jardin", "<PERSON><PERSON><PERSON>", "La ceja", "La estrella", "La pintada", "La sierra", "La union", "Liborina", "<PERSON><PERSON>", "<PERSON><PERSON>", "Medellin", "Montebello", "<PERSON><PERSON><PERSON>", "Mutata", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Nueva colonia", "Olaya", "Palermo", "Peñol", "Peque", "Pueblorrico", "Puerto berrio", "Puerto claver", "Puerto nare", "Puerto perales nuevo", "Puerto triunfo", "Puerto valdivia", "Remedios", "<PERSON><PERSON><PERSON>", "Rionegro (ant)", "Sabanalarga", "Sabaneta", "<PERSON><PERSON>", "San andres de cuerquia", "San antonio de prado", "San carlos", "San cristobal", "San francisco", "San jeronimo", "San jose de la montaña", "San juan de uraba", "San luis", "San pedro de los milagros", "San pedro de uraba", "San rafael", "San roque", "Santa barbara", "Santafe de antioquia", "Santa rosa de osos", "Santo <PERSON>o", "San vicente", "Segovia", "<PERSON><PERSON>", "Sopetran", "Tamesis", "<PERSON><PERSON>", "Tarso", "<PERSON><PERSON><PERSON><PERSON>", "Toledo", "Turbo", "<PERSON><PERSON><PERSON>", "Urrao", "Valdivia", "Valparaiso", "<PERSON><PERSON>", "Venecia", "Vigia del fuerte", "<PERSON><PERSON>", "Ya<PERSON>al", "Yolombo", "Zaragoza"]}, {"id": 39, "name": "Arauca", "country_id": 1, "department_code": null, "state_code": "ARA", "cities": ["Arauca", "Arauquita", "Cravo norte", "Fortul", "La esmeralda", "Puerto rondon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, {"id": 91, "name": "Archipielago de san andres", "country_id": 1, "department_code": null, "cities": ["Providencia - isla santa catalina", "San andres"], "state_code": "SAP"}, {"id": 22, "name": "Atlantico", "country_id": 1, "department_code": null, "state_code": "ATL", "cities": ["Aguada de pablo", "Baranoa", "<PERSON><PERSON><PERSON><PERSON>", "Campeche", "Campo de la cruz", "Candelaria", "Caracoli", "Cascajal", "<PERSON><PERSON>", "Juan <PERSON>", "<PERSON>a", "La peña", "Luruaco", "Malambo", "Manati", "<PERSON>r de varela", "<PERSON><PERSON><PERSON>", "Polonuevo", "Ponedera", "Puerto colombia", "Puerto giraldo", "Repelon", "Sabanagrande", "Sabanalarga", "<PERSON><PERSON>", "Santa cruz", "Santa lucia", "<PERSON>", "Soledad", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"id": 18, "name": "<PERSON><PERSON><PERSON>", "country_id": 1, "department_code": null, "state_code": "BOL", "cities": ["<PERSON><PERSON>", "Altos del rosario", "Arenal", "<PERSON><PERSON><PERSON><PERSON> b", "<PERSON><PERSON><PERSON>", "Barranco de loba", "Bayunca", "Calamar", "Cantagallo", "Cartagena", "Cascajal", "Cicuco", "<PERSON>lem<PERSON><PERSON> b", "Cordoba", "El carmen de bolivar", "El guamo (b)", "El peñon (bol)", "<PERSON><PERSON><PERSON><PERSON>", "Hatillo de loba", "<PERSON> arias", "Magangue", "Ma<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> la baja b", "Mompos", "Montecristo bolivar", "<PERSON> (b)", "<PERSON><PERSON><PERSON>", "Pasacaballos", "<PERSON><PERSON><PERSON><PERSON>", "Pontezuela", "Punta de cartagena", "Regidor", "Rio viejo", "San cristobal", "San estanislao", "<PERSON> fernando (b)", "San jacinto", "San jacinto del cauca", "San juan nepomu<PERSON>no", "San martin", "San martin de loba", "San pablo", "<PERSON> ana (b)", "Santa catalina", "Santa rosa de lima", "Santa rosa del sur", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> b", "Talaigua nuevo", "<PERSON><PERSON><PERSON><PERSON> nuevo", "Turbaco b", "Turbana", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zambrano"]}, {"id": 15, "name": "Boyaca", "country_id": 1, "department_code": null, "state_code": "BOY", "cities": ["Almeida", "Aquitania", "Arcabuco", "Belen", "Belencito", "<PERSON><PERSON><PERSON>", "Beteitiva", "Bo<PERSON><PERSON>", "Boyaca", "<PERSON><PERSON><PERSON> (b)", "Buenavista", "Busbanza", "Caldas", "Campohermoso", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chiquinquira", "Chiquiza", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chitaraque", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Cienega", "Combita", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Covarachia", "Cubara", "Cucaita", "Cuitiva", "<PERSON><PERSON><PERSON>", "El cocuy", "El espino", "Firavitoba", "Flores<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Gameza", "Garagoa", "Guacamayas", "Guateque", "Guayata", "Guican", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Labranzagrande", "La capilla", "La uvita", "<PERSON> victoria (b)", "Macanal", "Mari<PERSON>", "Miraflores", "Mongua", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nuevo colon", "Oicata", "Otan<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pajarito", "Panqueba", "<PERSON><PERSON>", "Paya", "Paz de rio", "Pesca", "Pisba", "Puerto boyaca", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ra<PERSON><PERSON>", "<PERSON><PERSON>", "Saboya", "Sachica", "Samaca", "San eduardo", "San jose de pare", "San luis de gaceno", "San mateo", "San miguel de sema", "San pablo de borbur", "Santa barbara", "Santa maria", "<PERSON>", "Santa rosa de viterbo", "Santa sofia", "Sativanorte", "Sa<PERSON><PERSON><PERSON>", "Siachoque", "So<PERSON>", "Socha", "Socota", "Sogamoso", "Somondoco", "<PERSON>ra", "<PERSON><PERSON><PERSON>", "Sotaquira", "Susacon", "<PERSON><PERSON><PERSON><PERSON>", "Sutatenza", "Tasco", "<PERSON><PERSON>", "Tibana", "Tibasosa", "Tierra negra", "Tinjaca", "Tipacoque", "Toca", "Togui", "Topaga", "To<PERSON>", "Trinidad", "Tunja", "<PERSON><PERSON><PERSON><PERSON>", "Turmeque", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Umbita", "Ventaquemada", "Villa de leyva", "Viracacha", "Zetaquira"]}, {"id": 5, "name": "Caldas", "country_id": 1, "department_code": null, "state_code": "CAL", "cities": ["Aguadas", "Alto tablazo", "Anserma", "<PERSON><PERSON><PERSON>", "Arauca", "Bajo tablazo", "Belalcazar (cl)", "Bolivia", "Chinchina", "Filadelfia", "Florencia", "Guarinocito", "La dorada", "La linda", "La merced", "Man<PERSON><PERSON>", "Manzanares", "<PERSON><PERSON><PERSON>", "Marquetalia", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Norcasia", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pensilvania", "<PERSON><PERSON><PERSON>", "Risaralda", "Salamina", "<PERSON><PERSON>", "Samaria", "<PERSON> jose (c)", "Supia", "Victoria", "<PERSON><PERSON><PERSON>", "Viterbo"]}, {"id": 38, "name": "<PERSON><PERSON><PERSON>", "country_id": 1, "department_code": null, "state_code": "CAQ", "cities": ["Albania", "Belen de los andaquies", "Campo hermoso", "Cartagena del chaira", "<PERSON><PERSON><PERSON>", "El doncello", "El paujil", "Florencia", "La montañita", "Milan", "Morelia", "Puerto rico", "San jose del fragua", "San vicente del caguan", "Solano", "Solita", "Valparaiso (caq)"]}, {"id": 7, "name": "<PERSON><PERSON><PERSON>", "country_id": 1, "department_code": null, "state_code": "CAS", "cities": ["Aguazul", "Chameza", "Hato corozal", "La chaparrera", "La salina", "<PERSON><PERSON>", "Monterrey", "Nunchia", "Orocue", "Paz de ariporo", "<PERSON><PERSON>", "Recetor", "Sabanalarga", "Sacama", "San luis de palenque", "Tamara", "<PERSON><PERSON><PERSON>", "Trinidad", "<PERSON><PERSON><PERSON> (c)", "<PERSON><PERSON>"]}, {"id": 28, "name": "Cauca", "country_id": 1, "department_code": null, "state_code": "CAU", "cities": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (c)", "Balboa (c)", "Belalcazar (ca)", "<PERSON><PERSON><PERSON> (c)", "Buenos aires", "Cajibio", "Caldono", "<PERSON><PERSON>", "Coconuco", "Corinto", "El bordo", "El estrecho (c)", "El plateado", "El tambo (ca)", "Florencia", "Guachen<PERSON>", "Guapi", "Inza", "Jambalo", "La sierra", "La vega", "Mercaderes", "Micay", "<PERSON>", "Mondomo", "<PERSON> (c)", "Ortigal", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Paispamba", "<PERSON><PERSON> (c)", "Piamonte", "Piendamo", "<PERSON><PERSON><PERSON>", "Puerto tejada", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "San sebastian", "<PERSON><PERSON>", "Santa rosa", "Santiago", "<PERSON>l<PERSON>", "Sotara", "Su<PERSON>z", "<PERSON><PERSON>", "Timba", "Tim<PERSON>", "Timbiqui", "<PERSON><PERSON>", "Totoro", "Tunia", "Villa rica"]}, {"id": 4, "name": "Cesar", "country_id": 1, "department_code": null, "state_code": "CES", "cities": ["Aguachica", "<PERSON><PERSON><PERSON>", "Be<PERSON><PERSON>", "Bosconia", "Casacaza", "Chimichagua", "Chiriguana", "<PERSON><PERSON><PERSON>", "Curumani", "<PERSON>", "El juncal", "El paso", "Gamarra", "<PERSON>", "La gloria", "La jagua de ibirico", "La loma", "La mata", "La paz", "La sierra", "Las vegas", "<PERSON><PERSON><PERSON> (c)", "<PERSON> drum<PERSON> pribbenow", "<PERSON><PERSON><PERSON>", "Pelaya", "Pueblo bello", "Rio de oro", "San alberto", "San diego", "San martin (c)", "San roque", "Tamalameque", "Valledupar"]}, {"id": 54, "name": "Choco", "country_id": 1, "department_code": null, "state_code": "CHO", "cities": ["<PERSON><PERSON><PERSON>", "Alto baudo", "<PERSON><PERSON>", "Bahia solano mutis", "<PERSON><PERSON> baudo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Carmen del <PERSON>rien", "<PERSON><PERSON><PERSON><PERSON>", "Condoto", "El cantón de san pablo", "El carmen de atrato", "Istmina", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Medio atrato", "Medio baudó", "Medio san juan", "Novita", "Nuquí", "Nuquí", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Río iro", "Rio quito", "<PERSON><PERSON><PERSON>", "Sipí", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Union panamericana", "<PERSON><PERSON>"]}, {"id": 20, "name": "Cordoba", "country_id": 1, "department_code": null, "state_code": "COR", "cities": ["Ayapel", "<PERSON><PERSON><PERSON><PERSON> (cor)", "Canalete", "Carrillo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> (c)", "<PERSON><PERSON>", "Cienaga de oro", "Cordoba", "Cotorra", "El porvenir (c)", "La apartada y la frontera", "Lorica", "Los cordobas", "Los garzones", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Montelibano", "Monteria", "Planeta rica", "Pueblo nuevo", "Puerto escondido", "Puerto libertador", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "San andres de sotavento", "San antero", "San bernardo del viento", "San carlos", "San pelayo", "Tierralta", "Tuchin", "Valencia"]}, {"id": 3, "name": "Cundinamarca", "country_id": 1, "department_code": null, "state_code": "CUN", "cities": ["Agua de dios", "Alban", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Apulo", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bogota", "Bojaca", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Cachipay", "Cajica", "Cambao", "<PERSON><PERSON><PERSON>", "Capellania", "Caqueza", "<PERSON>", "Chaguani", "<PERSON><PERSON>", "Chinauta", "Chipaque", "Choachi", "Choconta", "Cogua", "Cota", "Cucun<PERSON>", "El colegio (mesitas)", "El peñon", "El rosal", "El triunfo", "Facatativa", "Fomeque", "Fosca", "Funza", "Fuquene", "Fusagasuga", "Gachala", "Gach<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gama", "<PERSON><PERSON><PERSON><PERSON>", "Granada", "Guachet<PERSON>", "Guaduas", "Guasca", "Guataqui", "Guatavita", "Guayabal de siquima", "Guayabetal", "Gutierrez", "Jerusalen", "<PERSON><PERSON>", "La calera", "La florida", "La gran vãa", "La mesa", "La palma", "La peña", "La vega", "La victoria", "Lenguazaque", "Limoncitos", "<PERSON><PERSON><PERSON>", "Madrid", "Manta", "Maya", "Medina", "Mosquera", "<PERSON><PERSON><PERSON>", "Nazareth", "Nemocon", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nocaima", "<PERSON><PERSON>", "Pa<PERSON>", "<PERSON><PERSON>", "Paratebueno", "Pasca", "Puente de piedra", "Puente quetame", "Puerto bogota", "Puerto salgar", "<PERSON><PERSON>", "Quebradanegra", "Quetame", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "San antonio del tequendama", "San bernardo", "San cayetano", "San francisco", "<PERSON> joaquin (c)", "San juan de rio seco", "Santandercito", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Sibate", "Silvania", "Simijaca", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Subachoque", "Subia", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Su<PERSON>", "<PERSON><PERSON><PERSON>", "Tabio", "Tausa", "<PERSON><PERSON>", "<PERSON><PERSON>", "T<PERSON><PERSON><PERSON>", "T<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tobia", "Tocaima", "Tocancipa", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ubaque", "Ubate", "Une", "Utica", "Venecia (c)", "Vergara", "<PERSON><PERSON>", "Villagomez", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Viota", "Yacopi", "Zipacon", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 92, "name": "Guainia", "country_id": 1, "department_code": null, "state_code": "GUA", "cities": ["Barranco minas", "Puerto inirida"]}, {"id": 51, "name": "Guaviare", "country_id": 1, "department_code": null, "state_code": "GUV", "cities": ["Calamar", "El retorno", "La libertad", "Miraflores", "San jose del guaviare"]}, {"id": 49, "name": "<PERSON><PERSON>", "country_id": 1, "department_code": null, "state_code": "HUI", "cities": ["<PERSON><PERSON><PERSON>", "Agrado", "Aipe", "Algeciras", "Altamira", "<PERSON><PERSON>", "Belen (h)", "<PERSON><PERSON><PERSON><PERSON>", "Caguan", "Campoalegre", "Colombia", "<PERSON>", "Fortalecillas", "<PERSON><PERSON><PERSON>", "Gigante", "Guadalupe", "Guayabal (hul)", "Hobo", "Iquira", "La argentina", "La plata", "<PERSON><PERSON>", "Neiva", "Oporapa", "<PERSON><PERSON><PERSON>", "Paicol", "Palermo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Saladoblanco", "San agustin", "San jose de isnos", "Santa maria", "Suaza", "Tarqui", "Tell<PERSON>", "Teruel", "Tesalia", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Villavieja", "Yaguara", "Zuluaga"]}, {"id": 9, "name": "La guajira", "country_id": 1, "department_code": null, "state_code": "LAG", "cities": ["Albania", "Barrancas", "<PERSON><PERSON><PERSON>", "Distraccion", "El molino", "Fonseca", "Hatonuevo", "La jagua del pilar", "La mina", "La paz", "Maicao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Paraguachon", "<PERSON><PERSON><PERSON>", "San juan del cesar", "Uribia", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"id": 16, "name": "Magdalena", "country_id": 1, "department_code": null, "state_code": "MAG", "cities": ["Algarrobo", "Aracataca", "Ariguani", "Buritaca", "Carreto", "Cerro san antonio", "<PERSON><PERSON><PERSON>", "Cienaga", "Concordia", "El banco", "El dificil", "El piñon", "El reten", "Fundacion", "<PERSON><PERSON><PERSON>", "Guacamayal", "Guachaca", "Guamal (mag)", "La gran via", "Minca", "Nueva granada (m)", "Palermo", "Pedraza", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Puebloviejo", "Remolino", "Sabanas", "Salamina", "San angel", "San sebas<PERSON> de buenavista", "Santa ana (m)", "Santa barbara de pinto", "Santa marta", "Santa rosalia", "San zenon", "Sitionuevo", "Sitio nuevo", "Taganga", "Tenerife", "Zapayan", "Zona bananera"]}, {"id": 1, "name": "Meta", "country_id": 1, "department_code": null, "state_code": "MET", "cities": ["Acacias", "Alto pompeya", "Apiay", "Barranca de upia", "<PERSON><PERSON><PERSON><PERSON>", "Castilla la nueva", "Cubarral", "Cumaral", "El calvario", "El castillo", "El dorado", "Fuente de oro", "Granada (m)", "Guamal", "La julia", "La macarena", "La palmera", "<PERSON><PERSON><PERSON>", "Mapiripan", "Mesetas", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Puerto concordia", "Puerto gaitan", "Puerto lleras", "Puerto lopez", "Puerto rico", "Restrepo", "Rubiales", "San carlos de guaroa", "San juan de arama", "San juan del losada", "San juanito", "San lorenzo", "San martin", "Uribe", "Villavice<PERSON><PERSON>", "Vistahermosa"]}, {"id": 40, "name": "<PERSON><PERSON><PERSON>", "country_id": 1, "department_code": null, "state_code": "NAR", "cities": ["Alban", "Aldana", "Ancuya", "Arboleda", "Barbacoas", "Belen", "Be<PERSON><PERSON><PERSON>", "Bocas de satinga", "Buesaco", "Carlosama", "Catambuco", "<PERSON><PERSON><PERSON><PERSON>", "Colon", "Consaca", "<PERSON><PERSON><PERSON>", "Cordoba n", "Cuaspud nucleo", "Cumbal", "Cumbitara", "El charco", "El encano", "El peñol", "El rosario", "El tablón de gomez", "El tambo (na)", "Francisco p<PERSON>rro", "<PERSON>es", "Genova (n)", "Guachaves", "Guachucal", "Guaitarilla", "Gualmat<PERSON>", "Iles", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Is<PERSON><PERSON><PERSON>", "La cruz", "La florida (n)", "La llanada", "La tola", "La union", "Leiva", "Linares", "<PERSON><PERSON><PERSON>", "Los andes", "<PERSON><PERSON><PERSON><PERSON>", "Mallama", "Mosquera", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> herrera", "Ospina", "Pasto", "Piedrancha", "Policarpa", "Po<PERSON>i", "Providencia", "Puerres", "<PERSON><PERSON><PERSON><PERSON>", "Ricaurte (n)", "Ricaurte (na)", "<PERSON>", "<PERSON><PERSON><PERSON>", "San bernardo", "Sandona", "San jose", "San lorenzo", "San pablo", "San pedro de cartago", "Santa barbara", "Santa cruz", "<PERSON><PERSON><PERSON>", "Sotomayor", "Taminango", "<PERSON><PERSON>", "Tumaco", "Tu<PERSON><PERSON>", "Yacuanquer"]}, {"id": 17, "name": "Norte de santander", "country_id": 1, "department_code": null, "state_code": "NSA", "cities": ["Abrego", "Aguas claras", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bucarasica", "C<PERSON><PERSON>", "Cacota", "Campo dos", "Chinacota", "<PERSON><PERSON><PERSON>", "Convencion", "Cucuta", "<PERSON><PERSON><PERSON><PERSON>", "Durania", "El carmen", "El tarra", "El zulia", "Gramalote", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Labateca", "La donjuana", "La esperanza", "La floresta", "La gabarra", "La jarra", "La playa", "La vega", "Los patios", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Ocaña", "Pamplona", "Pamplonita", "Puerto santander", "Ragonvalia", "Salazar", "<PERSON> bernardo de bata", "San calixto", "San cayetano", "Santiago", "Sardinata", "Silos", "Teorama", "Tibu", "Toledo", "Villa caro", "Villa del rosario"]}, {"id": 67, "name": "<PERSON><PERSON><PERSON>", "country_id": 1, "department_code": null, "state_code": "PUT", "cities": ["Colon", "<PERSON> (p)", "La hormiga", "<PERSON><PERSON><PERSON><PERSON>", "Mocoa", "<PERSON><PERSON>", "Puerto asis", "Puerto caicedo", "Puerto guzman", "San francisco", "<PERSON>", "Santiago", "Sibundoy", "Villagarzon"]}, {"id": 19, "name": "<PERSON><PERSON><PERSON>", "country_id": 1, "department_code": null, "state_code": "QUI", "cities": ["Armenia (q)", "Barcelona", "Buenavista", "Calarca", "Circasia", "Cordoba", "El caimo", "Filand<PERSON>", "<PERSON><PERSON> (q)", "La tebaida", "Montenegro", "Pijao", "Pueblo tapado", "Quimbaya", "Salento"]}, {"id": 21, "name": "Risaralda", "country_id": 1, "department_code": null, "state_code": "RIS", "cities": ["Apia", "Balboa", "Belen de umbria", "Caimalito", "Do<PERSON><PERSON><PERSON>", "Guatica", "<PERSON><PERSON>", "La celia", "La virginia", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Pueblo rico", "Quinchia", "Santa rosa de cabal", "Santuario"]}, {"id": 8, "name": "Santander", "country_id": 1, "department_code": null, "state_code": "SAN", "cities": ["Acapulco", "Aguada", "Albania (s)", "Aratoca", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Barrancabermeja", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bucaramanga", "<PERSON><PERSON><PERSON>", "California (s)", "Capitanejo", "Carcasi", "Cepita", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Charta", "<PERSON><PERSON> (s)", "Chipata", "Cimitarra", "Cincelada", "Cite", "Concepcion", "Confines", "Contratacion", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "El carmen de chucuri", "El centro", "El guacamayo", "El llanito", "El peñon", "El playon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Floridablanca", "Gala<PERSON> (s)", "Gambita", "Giron", "Guaca", "Guadalupe", "Guapota", "Guavat<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> (s)", "<PERSON> maria", "Jordan sube", "La belleza", "La fortuna", "<PERSON><PERSON><PERSON>", "La palma", "La paz", "<PERSON><PERSON><PERSON>", "Los laureles", "Los santos", "<PERSON><PERSON><PERSON><PERSON>", "Malaga", "Matanza", "Mogotes", "Molag<PERSON><PERSON>", "Ocamonte", "Oiba", "<PERSON><PERSON><PERSON>", "Onzaga", "Palmar (s)", "Palmas del socorro", "<PERSON><PERSON> gordo", "Paramo", "Piedecuesta", "<PERSON><PERSON><PERSON>", "Puente nacional", "Puente sogamoso", "Puerto araujo", "Puerto parra", "Puerto wilches", "Rionegro", "Sabana de torres", "San andres", "San benito nuevo", "San gil", "San joaquin", "San jose de miranda", "San miguel", "San rafael", "Santa barbara", "Santa helena del opon", "San vicente de chucuri", "Simacota", "Socorro", "Sogamoso", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Surata", "Tienda nueva", "<PERSON><PERSON>", "Vado real", "Valle de san jose", "Velez", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ya<PERSON>", "Zapatoca"]}, {"id": 34, "name": "<PERSON><PERSON>", "country_id": 1, "department_code": null, "state_code": "SUC", "cities": ["Buenavista", "Caimit<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Corozal", "Coveñas", "El roble", "Galeras", "Guarand<PERSON>", "Guayabal", "La union (s)", "Los palmitos", "<PERSON><PERSON><PERSON>", "Morroa", "Ovejas", "Palmito", "Sam<PERSON><PERSON>", "San benito abad", "San juan de betulia", "San marcos", "San onofre", "San pedro", "Since", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (s)", "<PERSON><PERSON>", "<PERSON><PERSON> viejo"]}, {"id": 11, "name": "Tolima", "country_id": 1, "department_code": null, "state_code": "TOL", "cities": ["Alpujarra", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Armero", "Ataco", "Buenos aires", "Cajamarca", "<PERSON>", "Casabianca", "Chaparral", "Chicoral", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "C<PERSON><PERSON>", "<PERSON>", "Espinal", "Falan", "<PERSON><PERSON><PERSON>", "Fresno", "Gaitania", "<PERSON><PERSON><PERSON><PERSON>", "Guamo", "<PERSON>", "Herveo", "Honda", "Ibague", "<PERSON><PERSON><PERSON><PERSON>", "La arada", "<PERSON><PERSON>", "Libano", "Mariquita", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> herrera", "Ortega", "Pajonales", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Planadas", "Prado", "Purificacion", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saldaña", "San antonio", "San luis", "Santa isabel", "<PERSON> perez", "Su<PERSON>z", "Valle de san juan", "<PERSON><PERSON><PERSON><PERSON>", "Villahermosa", "Villarri<PERSON> (t)"]}, {"id": 12, "name": "Valle del Cauca", "alt_name": "Valle", "country_id": 1, "department_code": null, "cities": ["Alcala", "<PERSON><PERSON><PERSON>", "Andalucia", "Anserma nuevo", "Argelia (v)", "Bahía m<PERSON>", "<PERSON>jo calima", "Bolivar (v)", "<PERSON><PERSON><PERSON>", "Buenaventura", "Buga", "Bugalagrande", "Caicedonia", "Cali", "Candelaria", "Cartago", "Cascajal i", "Dagua", "Darien (calima)", "El aguila", "El cabuyal (v)", "El cairo", "El carmelo", "El cerrito", "El dovio", "El placer (v)", "El queremal", "El saladito", "Florida", "Ginebra", "Guabitas", "G<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "La cumbre", "La paila", "La union (v)", "La victoria (v)", "Obando", "<PERSON><PERSON>", "Pradera", "Restrepo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "San antonio de los caballeros", "San pedro(v)", "Santa elena", "Sevilla", "Toro", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>be uribe", "Versalles", "<PERSON><PERSON><PERSON><PERSON>", "Villa gorgona", "Yotoco", "Yumbo", "Zaragoza (v)", "Zarzal"], "state_code": "VAC"}, {"id": 64, "name": "<PERSON><PERSON><PERSON>", "country_id": 1, "department_code": null, "state_code": "VAU", "cities": ["Caruru", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"id": 70, "name": "<PERSON><PERSON><PERSON>", "country_id": 1, "department_code": null, "state_code": "VID", "cities": ["<PERSON><PERSON><PERSON><PERSON>", "La primavera", "Puerto carreño", "Santa rosalia"]}]}