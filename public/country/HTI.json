{"name": "Haiti", "iso3": "HTI", "iso2": "HT", "phone_code": "509", "capital": "Port-au-Prince", "currency": "HTG", "currency_name": "Haitian gourde", "currency_symbol": "G", "emoji": "🇭🇹", "emojiU": "U+1F1ED U+1F1F9", "translations": {"kr": "아이티", "br": "Haiti", "pt": "Haiti", "nl": "<PERSON><PERSON><PERSON>", "hr": "Haiti", "fa": "هائیتی", "de": "Haiti", "es": "Haiti", "fr": "<PERSON><PERSON><PERSON>", "ja": "ハイチ", "it": "Haiti", "cn": "海地", "tr": "Haiti"}, "states": [{"name": "Artibonite", "state_code": "AR", "cities": ["Anse <PERSON>", "Arrondissement de Saint-Marc", "Désarmes", "Dessalines", "Ennery", "Gonaïves", "Grande Saline", "<PERSON><PERSON>", "Marmelade", "Saint-<PERSON>", "Verrettes"]}, {"name": "Centre", "state_code": "CE", "cities": ["Arrondissement de Cerca La Source", "Cerca la Source", "<PERSON><PERSON><PERSON>", "Lascahobas", "Mayisad", "<PERSON><PERSON><PERSON><PERSON>", "Thomas<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"name": "Grand'<PERSON><PERSON>", "state_code": "GA", "cities": ["Anse-<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Les Abricots", "Les Irois", "<PERSON><PERSON>", "Petite Rivière de Nippes"]}, {"name": "<PERSON><PERSON><PERSON>", "state_code": "NI", "cities": ["<PERSON><PERSON><PERSON>", "Baradères", "Miragoâne", "Petit Trou de Nippes"]}, {"name": "Nord", "state_code": "ND", "cities": ["Acul du Nord", "Arrondissement de la Grande Rivière du Nord", "Arrondissement de Plaisance", "Arrondissement du Borgne", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Grande Rivière du Nord", "<PERSON><PERSON>", "Limonade", "<PERSON><PERSON>", "Okap", "<PERSON><PERSON>", "Pilate", "Plaine du Nord", "Plaisance", "Port-Margot", "Quart<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON><PERSON>"]}, {"name": "Nord-Est", "state_code": "NE", "cities": ["Arrondissement de Fort Liberté", "Arrondissement du Trou du Nord", "Caracol", "Carice", "<PERSON><PERSON><PERSON>", "Ferrier", "Fort Liberté", "Montòrganize", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Trou du Nord", "Wanament"]}, {"name": "Nord-Ouest", "state_code": "NO", "cities": ["<PERSON><PERSON><PERSON><PERSON>", "Arrondissement de Port-de-Paix", "Arrondissement de Saint-Louis du Nord", "Arrondissement du Môle Saint-Nicolas", "<PERSON><PERSON>", "Bombardopolis", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Môle Saint-Nicolas", "<PERSON><PERSON> An<PERSON>", "Port-de-Paix", "Saint-Louis du Nord", "Ti Port-de-Paix"]}, {"name": "Ouest", "state_code": "OU", "cities": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Arrondissement de Croix des Bouquets", "Arrondissement de Léogâne", "Arrondissement de Port-au-Prince", "<PERSON><PERSON><PERSON>", "Carrefour", "Cornillon", "Croix-des-Bouquets", "<PERSON><PERSON> 73", "Fond <PERSON>", "<PERSON>ond<PERSON>", "Grangwav", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Léogâne", "Pétionville", "Port-au-Prince", "<PERSON><PERSON><PERSON>", "Tigwav"]}, {"name": "Sud", "state_code": "SD", "cities": ["<PERSON><PERSON>", "Arrondissement de Port-Salut", "Arrondissement des Cayes", "Cavaillon", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Fond des Blancs", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Port-à-Piment", "Roche-à-Bateau", "Saint-<PERSON> du Sud", "Tiburon", "Torbeck"]}, {"name": "Sud-Est", "state_code": "SE", "cities": ["Anse-à-<PERSON><PERSON>", "Arrondissement de Bainet", "Arrondissement de Jacmel", "<PERSON><PERSON><PERSON><PERSON>", "Cayes-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kotdefè", "Marigot", "<PERSON><PERSON><PERSON>"]}]}