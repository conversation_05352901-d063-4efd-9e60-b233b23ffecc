{"name": "Luxembourg", "iso3": "LUX", "iso2": "LU", "phone_code": "352", "capital": "Luxembourg", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "emoji": "🇱🇺", "emojiU": "U+1F1F1 U+1F1FA", "translations": {"kr": "룩셈부르크", "br": "Luxemburgo", "pt": "Luxemburgo", "nl": "Luxemburg", "hr": "Luksemburg", "fa": "لوکزامبورگ", "de": "Luxemburg", "es": "Luxemburgo", "fr": "Luxembourg", "ja": "ルクセンブルク", "it": "Lussemburgo", "cn": "卢森堡", "tr": "Lüksemburg"}, "states": [{"name": "Canton of Capellen", "state_code": "CA", "cities": ["Bascharage", "<PERSON><PERSON>", "<PERSON>llen", "C<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Eischen", "<PERSON><PERSON><PERSON><PERSON>", "Hautcharage", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Schouweiler", "Septfontaines", "<PERSON><PERSON>"]}, {"name": "Canton of Clervaux", "state_code": "CL", "cities": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Troisvierges", "Weiswampach", "Wincrange"]}, {"name": "Canton of Diekirch", "state_code": "DI", "cities": ["Bettendorf", "<PERSON><PERSON><PERSON><PERSON>", "Commune de la Vallée de l’Ernz", "Diekirch", "<PERSON><PERSON>eldang<PERSON>", "Ettelbruck", "<PERSON><PERSON><PERSON>", "Medernach", "Mertzig", "Niederfeulen", "Reisdorf", "<PERSON><PERSON><PERSON><PERSON>", "Warken"]}, {"name": "Canton of Echternach", "state_code": "EC", "cities": ["Beaufort", "<PERSON><PERSON>", "Be<PERSON><PERSON>", "Consdorf", "Echternach", "Mompach", "Rosport", "<PERSON><PERSON><PERSON><PERSON>"]}, {"name": "Canton of Esch-sur-Alzette", "state_code": "ES", "cities": ["Aspelt", "Belvaux", "<PERSON><PERSON>", "Bettembourg", "<PERSON><PERSON><PERSON><PERSON>", "Differdange", "Dudelange", "Esch-sur-Alzette", "Frisange", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Leudelang<PERSON>", "Mondercange", "<PERSON><PERSON><PERSON><PERSON>", "Obercorn", "Pétange", "<PERSON><PERSON><PERSON>", "Reckange-sur-Mess", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Rumelange", "<PERSON><PERSON>", "Schifflang<PERSON>", "Soleuvre", "Tétange"]}, {"name": "Canton of Grevenmacher", "state_code": "GR", "cities": ["Betzdorf", "Biwer", "Flaxweiler", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mertert", "Wasserbillig", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"name": "Canton of Luxembourg", "state_code": "LU", "cities": ["<PERSON><PERSON><PERSON>", "Béreldange", "Bert<PERSON><PERSON>", "Contern", "Fentange", "Heisdorf", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Itzig", "<PERSON><PERSON><PERSON>", "Luxembourg", "Mout<PERSON>", "Müllendorf", "Niederanven", "<PERSON><PERSON>", "Sandweiler", "Schrassig", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Strassen", "Walferdange", "Weiler-la-Tour"]}, {"name": "Canton of Mersch", "state_code": "ME", "cities": ["Bissen", "Boevange-sur-Attert", "<PERSON><PERSON>", "Fischbach", "<PERSON><PERSON><PERSON>", "La<PERSON>chette", "Lintgen", "Lorentzweiler", "<PERSON><PERSON>", "Nommern", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"name": "Canton of Redange", "state_code": "RD", "cities": ["<PERSON><PERSON>", "Bettborn", "Commune de Préizerdaul", "<PERSON>l", "<PERSON><PERSON><PERSON><PERSON>", "Rambrouch", "Redange-sur-Attert", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, {"name": "Canton of Remich", "state_code": "RM", "cities": ["<PERSON><PERSON>", "Dalheim", "Lenningen", "Mondorf-les-Bains", "<PERSON><PERSON><PERSON>", "Schengen", "Stadtbredimus", "Waldbredimus"]}, {"name": "Canton of Vianden", "state_code": "VD", "cities": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vianden"]}, {"name": "Canton of Wiltz", "state_code": "WI", "cities": ["Bavigne", "<PERSON><PERSON><PERSON>", "Esch-sur-Sûre", "Goesdorf", "Kiischpelt", "Lac de la Haute-Sûre", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"name": "Diekirch District", "state_code": "D", "cities": []}, {"name": "Grevenmacher District", "state_code": "G", "cities": []}, {"name": "Luxembourg District", "state_code": "L", "cities": []}]}