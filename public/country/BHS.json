{"name": "The Bahamas", "iso3": "BHS", "iso2": "BS", "phone_code": "******", "capital": "Nassau", "currency": "BSD", "currency_name": "Bahamian dollar", "currency_symbol": "B$", "emoji": "🇧🇸", "emojiU": "U+1F1E7 U+1F1F8", "translations": {"kr": "바하마", "br": "Bahamas", "pt": "Baama<PERSON>", "nl": "Baham<PERSON>’s", "hr": "<PERSON><PERSON><PERSON>", "fa": "باهاما", "de": "Bahamas", "es": "Bahamas", "fr": "Bahamas", "ja": "バハマ", "it": "Bahamas", "cn": "巴哈马", "tr": "<PERSON><PERSON><PERSON><PERSON>"}, "states": [{"name": "<PERSON><PERSON><PERSON>", "state_code": "AK", "cities": []}, {"name": "Acklins and Crooked Islands", "state_code": "AC", "cities": []}, {"name": "Berry Islands", "state_code": "BY", "cities": []}, {"name": "<PERSON><PERSON><PERSON>", "state_code": "BI", "cities": ["Alice Town"]}, {"name": "Black Point", "state_code": "BP", "cities": []}, {"name": "Cat Island", "state_code": "CI", "cities": ["Arthur’s Town"]}, {"name": "Central Abaco", "state_code": "CO", "cities": ["Marsh Harbour"]}, {"name": "Central Andros", "state_code": "CS", "cities": []}, {"name": "Central Eleuthera", "state_code": "CE", "cities": []}, {"name": "Crooked Island", "state_code": "CK", "cities": ["Colonel <PERSON>"]}, {"name": "East Grand Bahama", "state_code": "EG", "cities": ["High Rock"]}, {"name": "<PERSON>uma", "state_code": "EX", "cities": ["George Town"]}, {"name": "Freeport", "state_code": "FP", "cities": ["Freeport", "<PERSON><PERSON>"]}, {"name": "Fresh Creek", "state_code": "FC", "cities": []}, {"name": "Governor's Harbour", "state_code": "GH", "cities": []}, {"name": "Grand Cay", "state_code": "GC", "cities": []}, {"name": "Green Turtle Cay", "state_code": "GT", "cities": []}, {"name": "Harbour Island", "state_code": "HI", "cities": ["Dunmore Town"]}, {"name": "High Rock", "state_code": "HR", "cities": []}, {"name": "Hope Town", "state_code": "HT", "cities": []}, {"name": "Inagua", "state_code": "IN", "cities": ["Matthew Town"]}, {"name": "Kemps Bay", "state_code": "KB", "cities": []}, {"name": "Long Island", "state_code": "LI", "cities": ["Clarence Town"]}, {"name": "Mangrove Cay", "state_code": "MC", "cities": []}, {"name": "Marsh Harbour", "state_code": "MH", "cities": []}, {"name": "Mayaguana District", "state_code": "MG", "cities": ["Abraham’s Bay"]}, {"name": "New Providence", "state_code": "NP", "cities": ["Nassau"]}, {"name": "Nichollstown and Berry Islands", "state_code": "NB", "cities": []}, {"name": "North Abaco", "state_code": "NO", "cities": ["Cooper’s Town"]}, {"name": "North Andros", "state_code": "NS", "cities": ["Andros Town", "San Andros"]}, {"name": "North Eleuthera", "state_code": "NE", "cities": []}, {"name": "Ragged Island", "state_code": "RI", "cities": ["Duncan Town"]}, {"name": "Rock Sound", "state_code": "RS", "cities": []}, {"name": "Rum Cay District", "state_code": "RC", "cities": ["Port Nelson"]}, {"name": "San Salvador and Rum Cay", "state_code": "SR", "cities": []}, {"name": "San Salvador Island", "state_code": "SS", "cities": ["Cockburn Town"]}, {"name": "Sandy Point", "state_code": "SP", "cities": []}, {"name": "South Abaco", "state_code": "SO", "cities": []}, {"name": "South Andros", "state_code": "SA", "cities": []}, {"name": "South Eleuthera", "state_code": "SE", "cities": []}, {"name": "Spanish Wells", "state_code": "SW", "cities": ["Spanish Wells"]}, {"name": "West Grand Bahama", "state_code": "WG", "cities": ["West End"]}]}