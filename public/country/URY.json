{"name": "Uruguay", "iso3": "URY", "iso2": "UY", "phone_code": "598", "capital": "Montevideo", "currency": "UYU", "currency_name": "Uruguayan peso", "currency_symbol": "$", "emoji": "🇺🇾", "emojiU": "U+1F1FA U+1F1FE", "translations": {"kr": "우루과이", "br": "Uruguai", "pt": "Uruguai", "nl": "Uruguay", "hr": "Urugvaj", "fa": "اروگوئه", "de": "Uruguay", "es": "Uruguay", "fr": "Uruguay", "ja": "ウルグアイ", "it": "Uruguay", "cn": "乌拉圭", "tr": "Uruguay"}, "states": [{"name": "Artigas Department", "state_code": "AR", "cities": ["Artigas", "Baltasar Brum", "Bella Unión", "Las Piedras", "<PERSON><PERSON>"]}, {"name": "Canelones Department", "state_code": "CA", "cities": ["Aguas Corrientes", "Atlántida", "Barra de Carrasco", "Barros Blancos", "Canelones", "Colonia <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "La Floresta", "La Paz", "Las Piedras", "Las Toscas", "Los Cerrillos", "<PERSON><PERSON>", "Montes", "Pando", "Paso de Carrasco", "Progreso", "San Antonio", "San Bautista", "San Jacinto", "San Ramón", "Santa Lucía", "Santa Rosa", "Sauce", "Soca", "<PERSON><PERSON>", "Toledo"]}, {"name": "Cerro Largo Department", "state_code": "CL", "cities": ["Aceguá", "<PERSON><PERSON><PERSON>", "Melo", "Río Branco", "Tupambaé"]}, {"name": "Colonia Department", "state_code": "CO", "cities": ["Carmel<PERSON>", "Colonia del Sacramento", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Nueva Helvecia", "Nueva Palmira", "Ombúes de Lavalle", "Rosario", "<PERSON><PERSON><PERSON>"]}, {"name": "Durazno Department", "state_code": "DU", "cities": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Durazno", "La Paloma", "Santa Bernard<PERSON>", "Sarandí <PERSON>", "Villa del Carmen"]}, {"name": "Flores Department", "state_code": "FS", "cities": ["Trinidad"]}, {"name": "Florida Department", "state_code": "FD", "cities": ["25 de Agosto", "25 de Mayo", "<PERSON>", "Cardal", "Casupá", "Florida", "Sarandí Grande"]}, {"name": "Lavalleja Department", "state_code": "LA", "cities": ["<PERSON>", "<PERSON>", "Mariscala", "Minas", "Solís de Mataojo"]}, {"name": "Maldonado Department", "state_code": "MA", "cities": ["Aiguá", "Maldonado", "Pan de Azúcar", "Piriápolis", "Punta del Este", "San Carlos"]}, {"name": "Montevideo Department", "state_code": "MO", "cities": ["Bella Vista", "Belvedere", "Buceo", "<PERSON><PERSON><PERSON> (Montevideo)", "Carrasco", "<PERSON><PERSON><PERSON>", "Centro", "Cerrito, Montevideo", "Colón Centro y Noroeste", "Colón Sudeste", "Conciliación", "Cordón", "Flor de Maroñas", "Ituzaingó", "La Unión", "Malvín", "Manga", "Maroñas", "Montevideo", "Nuevo París", "<PERSON><PERSON>", "Paso del Molino", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pocitos", "Punta Carretas", "<PERSON>unta Gorda", "Reducto", "Santiago Vázquez", "Sayage", "<PERSON><PERSON>ru<PERSON>", "Villa del Cerro", "Villa Española", "<PERSON>", "Villa Muñoz"]}, {"name": "Paysandú Department", "state_code": "PA", "cities": ["Estación Porvenir", "<PERSON><PERSON><PERSON><PERSON>", "Paysandú", "Piedras Coloradas", "Quebracho", "San Félix"]}, {"name": "Río Negro Department", "state_code": "RN", "cities": ["<PERSON><PERSON>", "Nuevo Berlín", "San Javier", "<PERSON>"]}, {"name": "Rivera Department", "state_code": "RV", "cities": ["Minas de Corrales", "<PERSON>", "Tranqueras", "<PERSON><PERSON><PERSON>"]}, {"name": "Rocha Department", "state_code": "RO", "cities": ["<PERSON><PERSON>", "Cebollatí", "<PERSON><PERSON>", "Die<PERSON><PERSON> de <PERSON>", "La Paloma", "Lascano", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"name": "Salto Department", "state_code": "SA", "cities": ["Belén", "Salto", "Villa Constitución"]}, {"name": "San José Department", "state_code": "SJ", "cities": ["Delta del Tigre", "<PERSON><PERSON><PERSON>", "Libertad", "Puntas de Valdéz", "<PERSON>", "<PERSON>", "San José de Mayo"]}, {"name": "Soriano Department", "state_code": "SO", "cities": ["Cardona", "<PERSON>", "<PERSON>", "Mercedes", "Palmitas", "Santa Catalina", "Villa Soriano"]}, {"name": "Tacuarembó Department", "state_code": "TA", "cities": ["Curtina", "Paso de los Toros", "Tacuarembó"]}, {"name": "Treinta y Tres Department", "state_code": "TT", "cities": ["Santa Clara de Olimar", "Treinta y Tres", "Vergara", "Villa Sara"]}]}