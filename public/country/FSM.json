{"name": "Micronesia", "iso3": "FSM", "iso2": "FM", "phone_code": "691", "capital": "Palikir", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "emoji": "🇫🇲", "emojiU": "U+1F1EB U+1F1F2", "translations": {"kr": "미크로네시아 연방", "br": "Micronésia", "pt": "Micronésia", "nl": "Micronesië", "hr": "Mikronezija", "fa": "ایالات فدرال میکرونزی", "de": "Mikronesien", "es": "Micronesia", "fr": "Micronésie", "ja": "ミクロネシア連邦", "it": "Micronesia", "cn": "密克罗尼西亚", "tr": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "states": [{"name": "Chuuk State", "state_code": "TRK", "cities": ["Eot Municipality", "Ettal Municipality", "Fananu Municipality", "Fanapanges Municipality", "Fefen Municipality", "Fonoton Municipality", "Houk Municipality", "Kuttu Municipality", "Lekinioch Municipality", "Losap Municipality", "Makur Municipality", "Moch Municipality", "<PERSON><PERSON><PERSON>", "Murilo Municipality", "Namoluk Municipality", "<PERSON><PERSON>", "Nema Municipality", "Nomwin Municipality", "Oneop Municipality", "Onou Municipality", "Onoun Municipality", "Paata-Tupunion Municipality", "Parem Municipality", "Piherarh Municipality", "Piis-Emwar Municipality", "Piis-Panewu Municipality", "Pollap Municipality", "Polowat Municipality", "Pwene Municipality", "Ramanum Municipality", "Ruo Municipality", "Satowan Municipality", "Siis Municipality", "Ta Municipality", "Tamatam Municipality", "Tolensom Municipality", "Tonoas Municipality", "Udot-Fonuweisom Municipality", "Uman-Fonuweisom Municipality", "Unanu Municipality", "<PERSON><PERSON>", "Weno Municipality", "Wonei Municipality"]}, {"name": "Kosrae State", "state_code": "KSA", "cities": ["Lelu Municipality", "Malem Municipality", "Tafunsak Municipality", "Tofol", "Utwe Municipality"]}, {"name": "Pohnpei State", "state_code": "PNI", "cities": ["Kitti Municipality", "Kolonia", "Kolonia Municipality", "Kolonia Town", "Madolenihm Municipality", "Mokil Municipality", "Nett Municipality", "Ngatik", "Nukuoro Municipality", "Palikir - National Government Center", "Pingelap Municipality", "Sapwuahfik Municipality", "Sokehs Municipality", "U Municipality"]}, {"name": "Yap State", "state_code": "YAP", "cities": ["Colonia", "Dalipebinaw Municipality", "Fais", "Fais Municipality", "Fanif Municipality", "Faraulep Municipality", "Gagil Municipality", "Gilman Municipality", "Kanifay Municipality", "Lamotrek Municipality", "Maap Municipality", "Ngulu Municipality", "Rull Municipality", "Rumung Municipality", "Satawal Municipality", "Tomil Municipality", "Ulithi Municipality", "Weloy Municipality", "Woleai Municipality"]}]}