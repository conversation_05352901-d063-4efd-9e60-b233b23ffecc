# Currency Rate Management Setup Guide

## 🚀 Quick Start

### 1. Environment Variables
Add these to your `.env` file:

```bash
# Currency Rate APIs (Optional - fallback will be used if not provided)
FIXER_API_KEY=your_fixer_api_key_here
CURRENCY_API_KEY=your_currency_api_key_here

# Scheduler Control
ENABLE_CURRENCY_SCHEDULER=true  # Set to false to disable auto-scheduler

# Database
DATABASE_URL=your_database_url_here
```

### 2. Database Migration
Run Prisma migration to create the currency rate tables:

```bash
npx prisma migrate dev --name add-currency-rates
npx prisma generate
```

### 3. Test the System

#### Test API Connectivity
```bash
curl http://localhost:3000/currency-rates/test-connectivity
```

#### Get Live Rate
```bash
curl http://localhost:3000/currency-rates/live-rate
```

#### Manual Rate Update (Development)
```bash
curl -X POST http://localhost:3000/currency-rates/test-update
```

#### Get Current Rates
```bash
curl http://localhost:3000/currency-rates/current
```

#### Convert Currency
```bash
curl -X POST http://localhost:3000/currency-rates/convert \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100,
    "fromCurrency": "USD",
    "toCurrency": "IDR",
    "type": "sell"
  }'
```

## 📊 System Architecture

### 1. **Main Service** (`dailyCurrencyRate.service.ts`)
- Fetches rates from multiple APIs
- Stores in database with profit margins
- Handles cron job updates

### 2. **Fallback Service** (`simpleCurrencyRate.service.ts`)
- Uses static rates when database unavailable
- Provides basic functionality for development
- No database dependency

### 3. **Scheduler** (`currencyRateScheduler.ts`)
- Runs daily at 6:00 AM Jakarta time
- Backup updates every 6 hours
- Graceful error handling

## 🔧 Configuration

### Profit Margins
Default margin is 2% (configurable in service):
- **Sell Rate**: `rate * (1 + margin)` - Rate when selling to customers
- **Buy Rate**: `rate * (1 - margin)` - Rate when buying from customers

### API Sources (in order of preference)
1. **ExchangeRate-API** (Free, no key required)
2. **Fixer.io** (Requires API key)
3. **CurrencyAPI** (Requires API key)

### Fallback Behavior
- If database fails → Use fallback service
- If all APIs fail → Use static rates (USD/IDR = 15,000)
- If scheduler fails → Manual update available

## 🛠️ Troubleshooting

### Common Issues

#### 1. "Cannot read properties of undefined (reading 'findMany')"
**Solution**: Database connection issue
```bash
# Check database connection
npx prisma db push
npx prisma generate

# Or disable scheduler temporarily
ENABLE_CURRENCY_SCHEDULER=false
```

#### 2. Scheduler not running
**Solution**: Check environment variables
```bash
# Enable scheduler
ENABLE_CURRENCY_SCHEDULER=true

# Check logs for startup messages
```

#### 3. API rate limits
**Solution**: Use multiple API keys or increase intervals
```javascript
// In currencyRateScheduler.ts
// Change from every 6 hours to every 12 hours
cron.schedule('0 */12 * * *', ...)
```

### Manual Operations

#### Force Update Rates
```bash
# Development
curl -X POST http://localhost:3000/currency-rates/test-update

# Production (requires admin auth)
curl -X POST http://localhost:3000/currency-rates/update \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

#### Check Rate History
```bash
curl "http://localhost:3000/currency-rates/history?from=USD&to=IDR&days=7"
```

#### Get Statistics
```bash
curl "http://localhost:3000/currency-rates/statistics?from=USD&to=IDR&days=30"
```

## 📈 Monitoring

### Health Check Endpoints
- `GET /currency-rates/current` - Check if rates are available
- `GET /currency-rates/test-connectivity` - Test external APIs
- `GET /currency-rates/live-rate` - Compare with live rates

### Logs to Monitor
```bash
# Successful updates
✅ Daily currency rate update completed successfully

# Database issues
❌ Database connection failed

# API issues
⚠️ ExchangeRate-API failed, trying Fixer.io

# Fallback usage
📝 Using fallback currency service
```

## 🔒 Security

### Production Considerations
1. **Disable test endpoints** in production
2. **Protect admin endpoints** with proper authentication
3. **Use environment variables** for API keys
4. **Monitor rate limits** on external APIs
5. **Set up alerts** for failed updates

### Rate Limiting
Consider implementing rate limiting for currency conversion endpoints:
```javascript
// Example rate limiting
app.use('/currency-rates/convert', rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
}));
```

## 📋 Maintenance

### Daily Tasks
- Monitor scheduler logs
- Check rate accuracy
- Verify API connectivity

### Weekly Tasks
- Review rate history
- Check profit margins
- Update API keys if needed

### Monthly Tasks
- Analyze rate statistics
- Review and adjust margins
- Update fallback rates if needed

## 🚨 Emergency Procedures

### If All APIs Fail
1. Check API status pages
2. Verify API keys
3. Use manual rate input
4. Increase fallback rate accuracy

### If Database Fails
1. System automatically uses fallback service
2. Fix database connection
3. Run manual update to sync rates
4. Verify data integrity

### If Scheduler Stops
1. Check server logs
2. Restart scheduler manually
3. Run manual rate update
4. Verify cron job configuration

## 📞 Support

For issues or questions:
1. Check logs first
2. Test connectivity endpoints
3. Verify environment variables
4. Use fallback service if needed
5. Contact development team with specific error messages
