# Payment Unique Constraint Fix

## Problem
The application was encountering a `Unique constraint failed on the constraint: Payment_orderId_key` error when trying to create payment records. This happened because:

1. Multiple payment creation attempts for the same order
2. Existing payment records with PENDING/EXPIRED/FAILED status were not being handled properly
3. The unique constraint on `orderId` in the Payment table prevented duplicate payment records

## Root Cause
The payment creation logic only checked for existing payments with "PAID" status, but didn't handle cases where:
- Payment was PENDING (user retrying payment)
- Payment was EXPIRED (payment timeout)
- Payment was FAILED (payment processing failed)

## Solution
Updated all payment creation methods to:

1. **Check for existing payments** for the order
2. **Allow retry for non-PAID payments** by deleting existing records
3. **Prevent duplicate payments** for already paid orders

### Files Modified

#### 1. `server/services/payment.service.ts`
- `createInvoice()` method
- `createEWalletCharge()` method  
- `createVirtualAccount()` method
- `createRetailOutlet()` method
- `createQRCode()` method

#### 2. `server/controllers/payment.controller.ts`
- `createPayment()` method
- `createInvoice()` method

#### 3. `server/services/paymentRedirect.service.ts`
- Payment creation logic

### Logic Flow
```typescript
// Check if payment already exists
const existingPayment = await prisma.payment.findFirst({
  where: { orderId },
});

if (existingPayment) {
  if (existingPayment.status === "PAID") {
    return errorResponse("Order already paid");
  }
  
  // If payment exists but not paid, delete it to create a new one
  if (existingPayment.status === "PENDING" || existingPayment.status === "EXPIRED" || existingPayment.status === "FAILED") {
    await prisma.payment.delete({
      where: { id: existingPayment.id }
    });
  }
}

// Proceed with creating new payment record
```

## Benefits
1. **Eliminates unique constraint errors** - Users can retry payments
2. **Prevents duplicate payments** - Orders can't be paid twice
3. **Improves user experience** - Failed payments can be retried seamlessly
4. **Maintains data integrity** - One payment record per order

## Testing
- Created comprehensive test that verifies the fix works correctly
- Confirmed that unique constraint error is properly handled
- Verified that payment replacement logic works as expected

## Status
✅ **FIXED** - All payment creation methods now handle existing payments correctly
