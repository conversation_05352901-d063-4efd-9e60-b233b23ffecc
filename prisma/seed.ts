import { PrismaClient } from '../generated/client'
import seedPaymentMethods from './seeders/paymentMethods.seeder'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create categories
  const categories = [
    {
      name: 'Sport',
      description: 'Sports trading cards and memorabilia',
      sellType: 'auction'
    },
    {
      name: 'Non Sport',
      description: 'Non-sports trading cards and collectibles',
      sellType: 'auction'
    },
    {
      name: 'Collectible',
      description: 'General collectible items',
      sellType: 'auction' 
    },
    {
      name: 'Gaming',
      description: 'Gaming cards and collectibles',
      sellType: 'auction'
    },
    {
      name: 'Entertainment',
      description: 'Movies, TV shows, and entertainment collectibles',
      sellType: 'auction'
    },
    {
      name: 'Vintage',
      description: 'Vintage and antique collectibles',
      sellType: 'buy-now'
    },
    {
      name: 'Art',
      description: 'Art and artistic collectibles',
      sellType: 'buy-now'
    },
    {
      name: 'Books & Comics',
      description: 'Books, comics, and literary collectibles',
      sellType: 'buy-now'
    },
    {
      name: 'Toys & Action Figures',
      description: 'Toys, action figures, and related collectibles',
      sellType: 'buy-now' 
    },
    {
      name: 'Technology',
      description: 'Technology and gadget collectibles',
      sellType: 'buy-now'
    }
  ]

  console.log('📦 Creating categories...')
  const createdCategories = []
  for (const category of categories) {
    const created = await prisma.category.upsert({
      where: { name: category.name },
      update: {},
      create: category
    })
    createdCategories.push(created)
    console.log(`✅ Created category: ${created.name}`)
  }

  // Create item types
  const itemTypes = [
    // Sport category
    {
      name: 'Baseball Cards',
      description: 'Baseball trading cards',
      categoryName: 'Sport'
    },
    {
      name: 'Basketball Cards',
      description: 'Basketball trading cards',
      categoryName: 'Sport'
    },
    {
      name: 'Football Cards',
      description: 'Football trading cards',
      categoryName: 'Sport'
    },
    {
      name: 'Soccer Cards',
      description: 'Soccer trading cards',
      categoryName: 'Sport'
    },
    {
      name: 'Sports Memorabilia',
      description: 'Sports memorabilia and autographs',
      categoryName: 'Sport'
    },
    
    // Non Sport category
    {
      name: 'Star Wars',
      description: 'Star Wars trading cards and collectibles',
      categoryName: 'Non Sport'
    },
    {
      name: 'Marvel',
      description: 'Marvel trading cards and collectibles',
      categoryName: 'Non Sport'
    },
    {
      name: 'DC Comics',
      description: 'DC Comics trading cards and collectibles',
      categoryName: 'Non Sport'
    },
    {
      name: 'Disney',
      description: 'Disney trading cards and collectibles',
      categoryName: 'Non Sport'
    },
    
    // Gaming category
    {
      name: 'Pokemon',
      description: 'Pokemon trading cards',
      categoryName: 'Gaming'
    },
    {
      name: 'Magic: The Gathering',
      description: 'Magic: The Gathering cards',
      categoryName: 'Gaming'
    },
    {
      name: 'Yu-Gi-Oh!',
      description: 'Yu-Gi-Oh! trading cards',
      categoryName: 'Gaming'
    },
    {
      name: 'Video Game Collectibles',
      description: 'Video game related collectibles',
      categoryName: 'Gaming'
    },
    
    // Entertainment category
    {
      name: 'Movie Memorabilia',
      description: 'Movie related memorabilia',
      categoryName: 'Entertainment'
    },
    {
      name: 'TV Show Collectibles',
      description: 'TV show related collectibles',
      categoryName: 'Entertainment'
    },
    {
      name: 'Music Memorabilia',
      description: 'Music related memorabilia',
      categoryName: 'Entertainment'
    },
    
    // Collectible category
    {
      name: 'Vintage Items',
      description: 'Vintage collectible items',
      categoryName: 'Collectible'
    },
    {
      name: 'Autographs',
      description: 'Autographed items',
      categoryName: 'Collectible'
    },
    {
      name: 'Coins',
      description: 'Collectible coins',
      categoryName: 'Collectible'
    },
    {
      name: 'Stamps',
      description: 'Collectible stamps',
      categoryName: 'Collectible'
    },

    // Vintage category
    {
      name: 'Vintage Postcards',
      description: 'Vintage postcards and ephemera',
      categoryName: 'Vintage'
    },
    {
      name: 'Vintage Toys',
      description: 'Vintage toys and games',
      categoryName: 'Vintage'
    },
    {
      name: 'Vintage Advertising',
      description: 'Vintage advertising materials',
      categoryName: 'Vintage'
    },

    // Art category
    {
      name: 'Original Art',
      description: 'Original artwork and sketches',
      categoryName: 'Art'
    },
    {
      name: 'Prints & Posters',
      description: 'Art prints and posters',
      categoryName: 'Art'
    },
    {
      name: 'Sculptures',
      description: 'Collectible sculptures',
      categoryName: 'Art'
    },

    // Books & Comics category
    {
      name: 'Comic Books',
      description: 'Comic books and graphic novels',
      categoryName: 'Books & Comics'
    },
    {
      name: 'First Edition Books',
      description: 'First edition and rare books',
      categoryName: 'Books & Comics'
    },
    {
      name: 'Manga',
      description: 'Japanese manga and light novels',
      categoryName: 'Books & Comics'
    },

    // Toys & Action Figures category
    {
      name: 'Action Figures',
      description: 'Action figures and collectible figures',
      categoryName: 'Toys & Action Figures'
    },
    {
      name: 'LEGO',
      description: 'LEGO sets and minifigures',
      categoryName: 'Toys & Action Figures'
    },
    {
      name: 'Model Kits',
      description: 'Model kits and scale models',
      categoryName: 'Toys & Action Figures'
    },

    // Technology category
    {
      name: 'Vintage Computers',
      description: 'Vintage computers and electronics',
      categoryName: 'Technology'
    },
    {
      name: 'Gaming Consoles',
      description: 'Vintage gaming consoles',
      categoryName: 'Technology'
    },
    {
      name: 'Gadgets',
      description: 'Collectible gadgets and devices',
      categoryName: 'Technology'
    }
  ]

  console.log('🏷️ Creating item types...')
  for (const itemType of itemTypes) {
    const category = createdCategories.find(c => c.name === itemType.categoryName)
    if (category) {
      const created = await prisma.itemType.upsert({
        where: { name: itemType.name },
        update: {},
        create: {
          name: itemType.name,
          description: itemType.description,
          categoryId: category.id
        }
      })
      console.log(`✅ Created item type: ${created.name}`)
    }
  }

  // Create a test user for products
  console.log('👤 Creating test user...')
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Seller',
      phoneNumber: '+1234567890',
      isEmailVerified: true,
      role: 'user'
    }
  })
  console.log(`✅ Created test user: ${testUser.email}`)

  // Helper function to generate slug
  const generateSlug = (itemName: string): string => {
    return itemName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
      .substring(0, 100);
  }

  // Sample products data
  const sampleProducts = [
    {
      itemName: "1986 Fleer Michael Jordan Rookie Card #57 PSA 9",
      description: "Iconic rookie card of basketball legend Michael Jordan. Graded PSA 9 MINT condition. This card is considered one of the most important basketball cards ever produced.",
      sellType: "auction",
      slug: "1986-fleer-michael-jordan-rookie-card-57-psa-9",
      priceUSD: 15000,
      categoryName: "Sport",
      itemTypeName: "Basketball Cards",
      auctionStartDate: new Date(),
      auctionEndDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      extendedBiddingEnabled: true,
      extendedBiddingMinutes: 5,
      extendedBiddingDuration: 10,
      status: "active",
      images: [
        {
          imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800",
          altText: "Michael Jordan Rookie Card Front",
          sortOrder: 0,
          isMain: true
        },
        {
          imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800",
          altText: "Michael Jordan Rookie Card Back",
          sortOrder: 1,
          isMain: false
        }
      ]
    },
    {
      itemName: "2009 Bowman Chrome Mike Trout Rookie Card #18T PSA 10",
      description: "Mike Trout's rookie card in perfect PSA 10 condition. One of the most sought-after modern baseball cards.",
      sellType: "auction",
      slug: "2009-bowman-chrome-mike-trout-rookie-card-18t-psa-10",
      priceUSD: 8500,
      categoryName: "Sport",
      itemTypeName: "Baseball Cards",
      auctionStartDate: new Date(),
      auctionEndDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
      extendedBiddingEnabled: false,
      status: "active",
      images: [
        {
          imageUrl: "https://images.unsplash.com/photo-1566577739112-5180d4bf9390?w=800",
          altText: "Mike Trout Rookie Card",
          sortOrder: 0,
          isMain: true
        }
      ]
    },
    {
      itemName: "1998 Pokemon Japanese Base Set Charizard Holo #6 PSA 9",
      description: "Original Japanese Charizard holographic card from the 1998 Base Set. Graded PSA 9 in excellent condition.",
      sellType: "buy-now",
      slug: "1998-pokemon-japanese-base-set-charizard-holo-6-psa-9",
      priceUSD: 2500,
      categoryName: "Gaming",
      itemTypeName: "Pokemon",
      status: "active",
      images: [
        {
          imageUrl: "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800",
          altText: "Charizard Pokemon Card",
          sortOrder: 0,
          isMain: true
        },
        {
          imageUrl: "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800",
          altText: "Charizard Pokemon Card Back",
          sortOrder: 1,
          isMain: false
        }
      ]
    },
    {
      itemName: "1977 Star Wars Luke Skywalker Action Figure MOC",
      description: "Vintage 1977 Kenner Luke Skywalker action figure, mint on card (MOC). Extremely rare in this condition.",
      sellType: "auction",
      priceUSD: 1200,
      slug: "1977-star-wars-luke-skywalker-action-figure-moc",
      categoryName: "Non Sport",
      itemTypeName: "Star Wars",
      auctionStartDate: new Date(),
      auctionEndDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
      extendedBiddingEnabled: true,
      extendedBiddingMinutes: 3,
      extendedBiddingDuration: 5,
      status: "active",
      images: [
        {
          imageUrl: "https://images.unsplash.com/photo-1601814933824-fd0b574dd592?w=800",
          altText: "Luke Skywalker Action Figure",
          sortOrder: 0,
          isMain: true
        }
      ]
    },
    {
      itemName: "Amazing Fantasy #15 Spider-Man First Appearance CGC 6.0",
      description: "The first appearance of Spider-Man from Amazing Fantasy #15 (1962). Graded CGC 6.0 Fine condition. A true holy grail of comic collecting.",
      sellType: "auction",
      slug: "amazing-fantasy-15-spider-man-first-appearance-cgc-6-0",
      priceUSD: 25000,
      categoryName: "Books & Comics",
      itemTypeName: "Comic Books",
      auctionStartDate: new Date(),
      auctionEndDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10 days from now
      extendedBiddingEnabled: true,
      extendedBiddingMinutes: 10,
      extendedBiddingDuration: 15,
      status: "active",
      images: [
        {
          imageUrl: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=800",
          altText: "Amazing Fantasy #15 Cover",
          sortOrder: 0,
          isMain: true
        },
        {
          imageUrl: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=800",
          altText: "Amazing Fantasy #15 Back Cover",
          sortOrder: 1,
          isMain: false
        }
      ]
    }
  ]

  console.log('🎯 Creating sample products...')
  for (const productData of sampleProducts) {
    const category = createdCategories.find(c => c.name === productData.categoryName)
    const itemType = await prisma.itemType.findFirst({
      where: { name: productData.itemTypeName }
    })

    if (category && itemType) {
      const slug = generateSlug(productData.itemName)

      const product = await prisma.product.create({
        data: {
          itemName: productData.itemName,
          slug: slug,
          description: productData.description,
          sellType: productData.sellType,
          priceUSD: productData.priceUSD,
          auctionStartDate: productData.auctionStartDate,
          auctionEndDate: productData.auctionEndDate,
          extendedBiddingEnabled: productData.extendedBiddingEnabled || false,
          extendedBiddingMinutes: productData.extendedBiddingMinutes,
          extendedBiddingDuration: productData.extendedBiddingDuration,
          status: productData.status,
          sellerId: testUser.id,
          categoryId: category.id,
          itemTypeId: itemType.id,
          images: {
            create: productData.images
          }
        },
        include: {
          images: true
        }
      })

      console.log(`✅ Created product: ${product.itemName} with ${product.images.length} images`)

      // Create some sample bids for auction items
      if (productData.sellType === 'auction') {
        const bidAmounts = [
          productData.priceUSD * 1.1,
          productData.priceUSD * 1.2,
          productData.priceUSD * 1.15
        ]

        for (let i = 0; i < bidAmounts.length; i++) {
          await prisma.bid.create({
            data: {
              productId: product.id,
              bidderId: testUser.id,
              amount: bidAmounts[i],
              isWinning: i === bidAmounts.length - 1, // Last bid is winning
              createdAt: new Date(Date.now() - (bidAmounts.length - i) * 60 * 60 * 1000) // Spread bids over time
            }
          })
        }

        // Update product with current bid and bid count
        await prisma.product.update({
          where: { id: product.id },
          data: {
            currentBid: Math.max(...bidAmounts),
            bidCount: bidAmounts.length
          }
        })

        console.log(`✅ Created ${bidAmounts.length} sample bids for ${product.itemName}`)
      }
    }
  }

  // Seed payment methods
  console.log('💳 Seeding payment methods...')
  await seedPaymentMethods()

  console.log('🎉 Database seeding completed!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
