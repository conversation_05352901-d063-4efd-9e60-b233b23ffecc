const { PrismaClient } = require('../generated/client')

const prisma = new PrismaClient()

const paymentMethodsData = [
  // USD Payment Methods
  {
    id: 'xendit_invoice_usd',
    name: 'All Payment Methods',
    description: 'Credit Card, Bank Transfer, E-Wallet & More',
    type: 'invoice',
    currency: 'USD',
    isActive: true,
    isRecommended: true,
    icon: '/payment-icons/xendit.png',
    processingFee: 2.9,
    processingFeeType: 'percentage',
    minAmount: 1,
    maxAmount: 100000,
    supportedCountries: ['US', 'ID', 'PH', 'MY', 'TH', 'VN'],
    features: ['instant_settlement', 'refund_support', 'recurring_payment'],
    xenditConfig: {
      paymentMethods: ['CREDIT_CARD', 'BANK_TRANSFER', 'EWALLET'],
      currency: 'USD'
    }
  },
  {
    id: 'credit_card_usd',
    name: 'Credit Card',
    description: 'Visa, Mastercard, JCB, AMEX',
    type: 'credit_card',
    currency: 'USD',
    isActive: true,
    isRecommended: false,
    icon: '/payment-icons/credit-card.png',
    processingFee: 2.9,
    processingFeeType: 'percentage',
    minAmount: 1,
    maxAmount: 100000,
    supportedCountries: ['US', 'ID', 'PH', 'MY', 'TH', 'VN'],
    features: ['instant_settlement', 'refund_support'],
    xenditConfig: {
      paymentMethods: ['CREDIT_CARD'],
      currency: 'USD'
    }
  },

  // IDR Payment Methods - Invoice
  {
    id: 'xendit_invoice_idr',
    name: 'All Payment Methods',
    description: 'Semua metode pembayaran tersedia',
    type: 'invoice',
    currency: 'IDR',
    isActive: true,
    isRecommended: true,
    icon: '/payment-icons/xendit.png',
    processingFee: 2.9,
    processingFeeType: 'percentage',
    minAmount: 10000,
    maxAmount: *********,
    supportedCountries: ['ID'],
    features: ['instant_settlement', 'refund_support', 'recurring_payment'],
    xenditConfig: {
      paymentMethods: ['CREDIT_CARD', 'BCA', 'BNI', 'BRI', 'MANDIRI', 'PERMATA', 'OVO', 'DANA', 'LINKAJA', 'SHOPEEPAY', 'ALFAMART', 'INDOMARET'],
      currency: 'IDR'
    }
  },

  // Virtual Account Methods
  {
    id: 'bca_va',
    name: 'BCA Virtual Account',
    description: 'Transfer via ATM/Mobile Banking BCA',
    type: 'virtual_account',
    currency: 'IDR',
    isActive: true,
    isRecommended: false,
    icon: '/payment-icons/bca.png',
    processingFee: 4000,
    processingFeeType: 'fixed',
    minAmount: 10000,
    maxAmount: *********,
    supportedCountries: ['ID'],
    features: ['24_hour_availability', 'instant_confirmation'],
    xenditConfig: {
      bankCode: 'BCA',
      currency: 'IDR'
    }
  },
  {
    id: 'bni_va',
    name: 'BNI Virtual Account',
    description: 'Transfer via ATM/Mobile Banking BNI',
    type: 'virtual_account',
    currency: 'IDR',
    isActive: true,
    isRecommended: false,
    icon: '/payment-icons/bni.png',
    processingFee: 4000,
    processingFeeType: 'fixed',
    minAmount: 10000,
    maxAmount: *********,
    supportedCountries: ['ID'],
    features: ['24_hour_availability', 'instant_confirmation'],
    xenditConfig: {
      bankCode: 'BNI',
      currency: 'IDR'
    }
  },
  {
    id: 'bri_va',
    name: 'BRI Virtual Account',
    description: 'Transfer via ATM/Mobile Banking BRI',
    type: 'virtual_account',
    currency: 'IDR',
    isActive: true,
    isRecommended: false,
    icon: '/payment-icons/bri.png',
    processingFee: 4000,
    processingFeeType: 'fixed',
    minAmount: 10000,
    maxAmount: *********,
    supportedCountries: ['ID'],
    features: ['24_hour_availability', 'instant_confirmation'],
    xenditConfig: {
      bankCode: 'BRI',
      currency: 'IDR'
    }
  },
  {
    id: 'mandiri_va',
    name: 'Mandiri Virtual Account',
    description: 'Transfer via ATM/Mobile Banking Mandiri',
    type: 'virtual_account',
    currency: 'IDR',
    isActive: true,
    isRecommended: false,
    icon: '/payment-icons/mandiri.png',
    processingFee: 4000,
    processingFeeType: 'fixed',
    minAmount: 10000,
    maxAmount: *********,
    supportedCountries: ['ID'],
    features: ['24_hour_availability', 'instant_confirmation'],
    xenditConfig: {
      bankCode: 'MANDIRI',
      currency: 'IDR'
    }
  },
  {
    id: 'permata_va',
    name: 'Permata Virtual Account',
    description: 'Transfer via ATM/Mobile Banking Permata',
    type: 'virtual_account',
    currency: 'IDR',
    isActive: true,
    isRecommended: false,
    icon: '/payment-icons/permata.png',
    processingFee: 4000,
    processingFeeType: 'fixed',
    minAmount: 10000,
    maxAmount: *********,
    supportedCountries: ['ID'],
    features: ['24_hour_availability', 'instant_confirmation'],
    xenditConfig: {
      bankCode: 'PERMATA',
      currency: 'IDR'
    }
  },

  // E-Wallet Methods
  {
    id: 'ovo',
    name: 'OVO',
    description: 'Bayar dengan OVO',
    type: 'ewallet',
    currency: 'IDR',
    isActive: true,
    isRecommended: true,
    icon: '/payment-icons/ovo.png',
    processingFee: 2.9,
    processingFeeType: 'percentage',
    minAmount: 10000,
    maxAmount: ********,
    supportedCountries: ['ID'],
    features: ['instant_payment', 'qr_code', 'deep_link'],
    xenditConfig: {
      ewalletType: 'OVO',
      currency: 'IDR'
    }
  },
  {
    id: 'dana',
    name: 'DANA',
    description: 'Bayar dengan DANA',
    type: 'ewallet',
    currency: 'IDR',
    isActive: true,
    isRecommended: true,
    icon: '/payment-icons/dana.png',
    processingFee: 2.9,
    processingFeeType: 'percentage',
    minAmount: 10000,
    maxAmount: ********,
    supportedCountries: ['ID'],
    features: ['instant_payment', 'qr_code', 'deep_link'],
    xenditConfig: {
      ewalletType: 'DANA',
      currency: 'IDR'
    }
  },
  {
    id: 'shopeepay',
    name: 'ShopeePay',
    description: 'Bayar dengan ShopeePay',
    type: 'ewallet',
    currency: 'IDR',
    isActive: true,
    isRecommended: true,
    icon: '/payment-icons/shopeepay.png',
    processingFee: 2.9,
    processingFeeType: 'percentage',
    minAmount: 10000,
    maxAmount: ********,
    supportedCountries: ['ID'],
    features: ['instant_payment', 'qr_code', 'deep_link'],
    xenditConfig: {
      ewalletType: 'SHOPEEPAY',
      currency: 'IDR'
    }
  },
  {
    id: 'gopay',
    name: 'GoPay',
    description: 'Bayar dengan GoPay',
    type: 'ewallet',
    currency: 'IDR',
    isActive: true,
    isRecommended: true,
    icon: '/payment-icons/gopay.png',
    processingFee: 2.9,
    processingFeeType: 'percentage',
    minAmount: 10000,
    maxAmount: ********,
    supportedCountries: ['ID'],
    features: ['instant_payment', 'qr_code', 'deep_link'],
    xenditConfig: {
      ewalletType: 'GOPAY',
      currency: 'IDR'
    }
  },

  // QR Code Methods
  {
    id: 'qris',
    name: 'QRIS',
    description: 'Scan QR dengan aplikasi bank/e-wallet',
    type: 'qr_code',
    currency: 'IDR',
    isActive: true,
    isRecommended: false,
    icon: '/payment-icons/qris.png',
    processingFee: 0.7,
    processingFeeType: 'percentage',
    minAmount: 1000,
    maxAmount: ********,
    supportedCountries: ['ID'],
    features: ['universal_qr', 'instant_payment', 'offline_capable'],
    xenditConfig: {
      qrCodeType: 'QRIS',
      currency: 'IDR'
    }
  },

  // Retail Outlet Methods
  {
    id: 'indomaret',
    name: 'Indomaret',
    description: 'Bayar di Indomaret terdekat',
    type: 'retail_outlet',
    currency: 'IDR',
    isActive: true,
    isRecommended: false,
    icon: '/payment-icons/indomaret.png',
    processingFee: 5000,
    processingFeeType: 'fixed',
    minAmount: 10000,
    maxAmount: 5000000,
    supportedCountries: ['ID'],
    features: ['cash_payment', 'nationwide_coverage', '24_hour_availability'],
    xenditConfig: {
      retailOutletName: 'INDOMARET',
      currency: 'IDR'
    }
  },
  {
    id: 'alfamart',
    name: 'Alfamart',
    description: 'Bayar di Alfamart terdekat',
    type: 'retail_outlet',
    currency: 'IDR',
    isActive: true,
    isRecommended: false,
    icon: '/payment-icons/alfamart.png',
    processingFee: 5000,
    processingFeeType: 'fixed',
    minAmount: 10000,
    maxAmount: 5000000,
    supportedCountries: ['ID'],
    features: ['cash_payment', 'nationwide_coverage', '24_hour_availability'],
    xenditConfig: {
      retailOutletName: 'ALFAMART',
      currency: 'IDR'
    }
  }
]

async function seedPaymentMethods() {
  console.log('🔄 Seeding payment methods...')

  try {
    // Clear existing payment methods
    await prisma.paymentMethod.deleteMany({})
    console.log('✅ Cleared existing payment methods')

    // Insert payment methods
    for (const paymentMethod of paymentMethodsData) {
      await prisma.paymentMethod.create({
        data: paymentMethod
      })
      console.log(`✅ Created payment method: ${paymentMethod.name} (${paymentMethod.currency})`)
    }

    console.log(`🎉 Successfully seeded ${paymentMethodsData.length} payment methods`)
  } catch (error) {
    console.error('❌ Error seeding payment methods:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

seedPaymentMethods()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })
